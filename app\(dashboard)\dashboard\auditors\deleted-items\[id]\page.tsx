// app/(dashboard)/dashboard/auditors/deleted-items/[id]/page.tsx
"use client"

import { useEffect, useState } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { DashboardShell } from "@/components/dashboard-shell"
import { Dash<PERSON>Header } from "@/components/dashboard-header"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { Skeleton } from "@/components/ui/skeleton"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { 
  ArrowLeft,
  FileText,
  User,
  Calendar,
  Shield,
  AlertTriangle,
  CheckCircle,
  RotateCcw,
  Download,
  Eye,
  Clock,
  Database,
  RefreshCw
} from "lucide-react"
import Link from "next/link"
import { AuditDateFormatter } from "@/lib/utils/date-formatter"
import { DeletedItem } from "@/lib/stores/audit-store"

interface DetailedDeletedItem extends DeletedItem {
  deletionContext?: {
    userAgent?: string;
    ipAddress?: string;
    sessionId?: string;
    requestId?: string;
  };
  auditTrail?: Array<{
    action: string;
    performedBy: string;
    performedAt: string;
    details: string;
  }>;
  relatedItems?: Array<{
    id: string;
    type: string;
    reference: string;
    relationship: string;
  }>;
  complianceChecks?: {
    retentionPolicyCompliant: boolean;
    approvalWorkflowCompleted: boolean;
    documentationComplete: boolean;
    governmentNotificationSent: boolean;
  };
}

export default function DeletedItemDetailPage() {
  const params = useParams()
  const router = useRouter()
  const [item, setItem] = useState<DetailedDeletedItem | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isRecovering, setIsRecovering] = useState(false)

  const itemId = params.id as string

  // Fetch detailed item data
  useEffect(() => {
    fetchItemDetails()
  }, [itemId])

  const fetchItemDetails = async () => {
    try {
      setIsLoading(true)
      setError(null)

      const response = await fetch(`/api/audit/deleted-items/${itemId}`)
      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('Deleted item not found')
        }
        throw new Error('Failed to fetch item details')
      }

      const data = await response.json()
      setItem(data.item)
    } catch (error) {
      console.error('Error fetching item details:', error)
      setError(error instanceof Error ? error.message : 'Failed to fetch item details')
    } finally {
      setIsLoading(false)
    }
  }

  const handleRecovery = () => {
    if (item) {
      router.push(`/dashboard/auditors/recovery-center?itemId=${item.id}`)
    }
  }

  const getStatusBadge = (reviewStatus: string) => {
    switch (reviewStatus) {
      case "approved":
        return <Badge className="bg-green-100 text-green-700"><CheckCircle className="h-3 w-3 mr-1" />Approved</Badge>
      case "pending":
        return <Badge className="bg-yellow-100 text-yellow-700"><Clock className="h-3 w-3 mr-1" />Pending Review</Badge>
      case "flagged":
        return <Badge className="bg-red-100 text-red-700"><AlertTriangle className="h-3 w-3 mr-1" />Flagged</Badge>
      case "investigated":
        return <Badge className="bg-blue-100 text-blue-700"><Eye className="h-3 w-3 mr-1" />Investigated</Badge>
      default:
        return <Badge variant="secondary">{reviewStatus}</Badge>
    }
  }

  const getComplianceFlags = (item: DetailedDeletedItem) => {
    const flags = [];
    if (item.originalData?.amount && item.originalData.amount > 100000) {
      flags.push("High Value Transaction");
    }
    if (item.originalModel === 'Income' || item.originalModel === 'Expenditure') {
      flags.push("Financial Record");
    }
    if (item.originalModel === 'Employee') {
      flags.push("HR Record", "Personal Data");
    }
    if (item.deletionType === 'bulk') {
      flags.push("Bulk Operation");
    }
    return flags;
  }

  if (isLoading) {
    return (
      <DashboardShell>
        <DashboardHeader
          heading="Deleted Item Details"
          text="Loading item details..."
        >
          <Link href="/dashboard/auditors/deleted-items">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to List
            </Button>
          </Link>
        </DashboardHeader>

        <div className="space-y-6">
          {/* Loading skeletons */}
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <Skeleton className="h-6 w-32" />
                <Skeleton className="h-4 w-48" />
              </CardHeader>
              <CardContent className="space-y-4">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-4 w-1/2" />
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <Skeleton className="h-6 w-32" />
              </CardHeader>
              <CardContent className="space-y-4">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
              </CardContent>
            </Card>
          </div>
        </div>
      </DashboardShell>
    )
  }

  if (error || !item) {
    return (
      <DashboardShell>
        <DashboardHeader
          heading="Deleted Item Details"
          text="Error loading item details"
        >
          <Link href="/dashboard/auditors/deleted-items">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to List
            </Button>
          </Link>
        </DashboardHeader>

        <div className="space-y-6">
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              {error || 'Item not found'}
            </AlertDescription>
          </Alert>
          <div className="flex gap-2">
            <Button onClick={fetchItemDetails} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
            <Link href="/dashboard/auditors/deleted-items">
              <Button>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to List
              </Button>
            </Link>
          </div>
        </div>
      </DashboardShell>
    )
  }

  const complianceFlags = getComplianceFlags(item)
  const daysUntilExpiry = AuditDateFormatter.daysUntil(item.recoveryDeadline)
  const canRecover = item?.canBeRecovered &&
    daysUntilExpiry > 0 &&
    item.reviewStatus !== 'flagged'

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Deleted Item Details"
        text={`Audit record for ${item.originalModel} - ${item.originalData?.reference || item.originalId}`}
      >
        <div className="flex items-center gap-2">
          {canRecover && (
            <Button onClick={handleRecovery} disabled={isRecovering}>
              <RotateCcw className="h-4 w-4 mr-2" />
              {isRecovering ? 'Processing...' : 'Recover Item'}
            </Button>
          )}
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Link href="/dashboard/auditors/deleted-items">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to List
            </Button>
          </Link>
        </div>
      </DashboardHeader>

      <div className="space-y-6">
        {/* Recovery Status Alert */}
        {item.canBeRecovered && (
          <Alert className={daysUntilExpiry <= 7 ? "border-red-200 bg-red-50" : "border-yellow-200 bg-yellow-50"}>
            <Clock className="h-4 w-4" />
            <AlertDescription>
              {daysUntilExpiry > 0 ? (
                <>
                  <strong>Recovery Available:</strong> This item can be recovered for {daysUntilExpiry} more days 
                  (until {AuditDateFormatter.dashboard(item.recoveryDeadline)}).
                  {daysUntilExpiry <= 7 && " Recovery deadline approaching!"}
                </>
              ) : (
                <strong>Recovery Expired:</strong> The recovery period for this item has ended.
              )}
            </AlertDescription>
          </Alert>
        )}

        {/* Main Content Grid */}
        <div className="grid gap-6 md:grid-cols-2">
          {/* Item Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5" />
                Item Information
              </CardTitle>
              <CardDescription>
                Original data and metadata for the deleted item
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-3">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Item Type</label>
                  <p className="text-sm font-medium">{item.originalModel}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Original ID</label>
                  <p className="text-sm font-mono">{item.originalId}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Reference</label>
                  <p className="text-sm">{item.originalData?.reference || item.originalData?.name || 'N/A'}</p>
                </div>
                {item.originalData?.amount && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Amount</label>
                    <p className="text-sm font-medium">
                      {new Intl.NumberFormat('en-MW', { 
                        style: 'currency', 
                        currency: 'MWK' 
                      }).format(item.originalData.amount)}
                    </p>
                  </div>
                )}
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Fiscal Year</label>
                  <p className="text-sm">{item.fiscalYear || 'N/A'}</p>
                </div>
              </div>

              <Separator />

              <div>
                <label className="text-sm font-medium text-muted-foreground mb-2 block">Original Data</label>
                <div className="bg-muted/50 rounded-md p-3">
                  <pre className="text-xs overflow-auto">
                    {JSON.stringify(item.originalData, null, 2)}
                  </pre>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Deletion Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Deletion Information
              </CardTitle>
              <CardDescription>
                Details about when and why this item was deleted
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-3">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Deleted By</label>
                  <p className="text-sm font-medium">{item.deletedBy?.name || 'Unknown'}</p>
                  <p className="text-xs text-muted-foreground">{item.deletedBy?.email}</p>
                  <p className="text-xs text-muted-foreground">{item.deletedBy?.role}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Deletion Date</label>
                  <p className="text-sm">{AuditDateFormatter.report(item.deletionDate)}</p>
                  <p className="text-xs text-muted-foreground">
                    {AuditDateFormatter.relative(item.deletionDate)}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Deletion Type</label>
                  <Badge variant="outline" className="text-xs">
                    {item.deletionType}
                  </Badge>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Recovery Deadline</label>
                  <p className="text-sm">{AuditDateFormatter.report(item.recoveryDeadline)}</p>
                  <p className="text-xs text-muted-foreground">
                    {daysUntilExpiry > 0 ? `${daysUntilExpiry} days remaining` : 'Expired'}
                  </p>
                </div>
              </div>

              <Separator />

              <div>
                <label className="text-sm font-medium text-muted-foreground mb-2 block">Deletion Reason</label>
                <div className="bg-muted/50 rounded-md p-3">
                  <p className="text-sm">{item.deletionReason}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Review Status and Compliance */}
        <div className="grid gap-6 md:grid-cols-2">
          {/* Review Status */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Review Status
              </CardTitle>
              <CardDescription>
                Audit review and compliance status
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Current Status</span>
                {getStatusBadge(item.reviewStatus)}
              </div>

              {item.reviewedBy && (
                <>
                  <Separator />
                  <div className="grid gap-2">
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Reviewed By</label>
                      <p className="text-sm">{item.reviewedBy.name}</p>
                      <p className="text-xs text-muted-foreground">{item.reviewedBy.role}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Review Date</label>
                      <p className="text-sm">{AuditDateFormatter.dashboard(item.reviewedAt || '')}</p>
                    </div>
                  </div>
                </>
              )}

              {item.auditNotes && (
                <>
                  <Separator />
                  <div>
                    <label className="text-sm font-medium text-muted-foreground mb-2 block">Audit Notes</label>
                    <div className="bg-muted/50 rounded-md p-3">
                      <p className="text-sm">{item.auditNotes}</p>
                    </div>
                  </div>
                </>
              )}
            </CardContent>
          </Card>

          {/* Compliance Flags */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Compliance Information
              </CardTitle>
              <CardDescription>
                Government compliance flags and requirements
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-muted-foreground mb-2 block">Compliance Flags</label>
                <div className="flex flex-wrap gap-2">
                  {complianceFlags.map((flag, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {flag}
                    </Badge>
                  ))}
                  {complianceFlags.length === 0 && (
                    <span className="text-sm text-muted-foreground">No special compliance requirements</span>
                  )}
                </div>
              </div>

              <Separator />

              <div className="grid gap-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Retention Policy Compliant</span>
                  <CheckCircle className="h-4 w-4 text-green-600" />
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Documentation Complete</span>
                  <CheckCircle className="h-4 w-4 text-green-600" />
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Audit Trail Maintained</span>
                  <CheckCircle className="h-4 w-4 text-green-600" />
                </div>
              </div>

              <Separator />

              <div>
                <label className="text-sm font-medium text-muted-foreground">Audit Record ID</label>
                <p className="text-sm font-mono">{item.auditRecordId}</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardShell>
  )
}
