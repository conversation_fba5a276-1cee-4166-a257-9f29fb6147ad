// app/(dashboard)/dashboard/auditors/deleted-items/page.tsx
"use client"

import { useEffect, useState } from "react"
import { DashboardShell } from "@/components/dashboard-shell"
import { DashboardHeader } from "@/components/dashboard-header"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Skeleton } from "@/components/ui/skeleton"
import {
  Trash2,
  Search,
  Download,
  Eye,
  RotateCcw,
  Shield,
  Clock,
  RefreshCw,
  AlertTriangle,
  Database
} from "lucide-react"
import Link from "next/link"
import { useAuditStore } from "@/lib/stores/audit-store"
import { AuditDateFormatter } from "@/lib/utils/date-formatter"

export default function DeletedItemsPage() {
  const {
    deletedItems,
    isLoadingItems,
    error,
    filters,
    page,
    totalPages,
    totalCount,
    fiscalYears,
    modelTypes,
    reviewStatuses,
    fetchDeletedItems,
    setFilters,
    setPage
  } = useAuditStore();

  // Local state for filter inputs
  const [searchInput, setSearchInput] = useState(filters.search || '');
  const [modelTypeFilter, setModelTypeFilter] = useState(filters.modelType || 'all');
  const [reviewStatusFilter, setReviewStatusFilter] = useState(filters.reviewStatus || 'all');
  const [fiscalYearFilter, setFiscalYearFilter] = useState(filters.fiscalYear || 'all');

  // Fetch data on component mount and when filters change
  useEffect(() => {
    fetchDeletedItems();
  }, [fetchDeletedItems, filters, page]);

  // Handle search input with debounce
  useEffect(() => {
    const timer = setTimeout(() => {
      setFilters({ search: searchInput });
    }, 500);

    return () => clearTimeout(timer);
  }, [searchInput, setFilters]);

  // Handle filter changes
  const handleModelTypeChange = (value: string) => {
    setModelTypeFilter(value);
    setFilters({ modelType: value === 'all' ? undefined : value });
  };

  const handleReviewStatusChange = (value: string) => {
    setReviewStatusFilter(value);
    setFilters({ reviewStatus: value === 'all' ? undefined : value });
  };

  const handleFiscalYearChange = (value: string) => {
    setFiscalYearFilter(value);
    setFilters({ fiscalYear: value === 'all' ? undefined : value });
  };

  // Handle refresh
  const handleRefresh = () => {
    fetchDeletedItems();
  };

  const getStatusBadge = (reviewStatus: string) => {
    switch (reviewStatus) {
      case "approved":
        return <Badge className="bg-green-100 text-green-700">Approved</Badge>
      case "pending":
        return <Badge className="bg-yellow-100 text-yellow-700">Pending Review</Badge>
      case "flagged":
        return <Badge className="bg-red-100 text-red-700">Flagged</Badge>
      case "investigated":
        return <Badge className="bg-blue-100 text-blue-700">Investigated</Badge>
      default:
        return <Badge variant="secondary">{reviewStatus}</Badge>
    }
  }

  // Use the audit date formatter utility
  const getDaysUntilExpiry = (deadline: string) => {
    return AuditDateFormatter.daysUntil(deadline);
  }

  const getComplianceFlags = (item: any) => {
    const flags = [];
    if (item.originalData?.amount && item.originalData.amount > 100000) {
      flags.push("High Value");
    }
    if (item.originalModel === 'Income' || item.originalModel === 'Expenditure') {
      flags.push("Financial Record");
    }
    if (item.originalModel === 'Employee') {
      flags.push("HR Record", "Personal Data");
    }
    return flags;
  };

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Deleted Items"
        text="View and manage all deleted items in the audit trail"
      >
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={handleRefresh} disabled={isLoadingItems}>
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoadingItems ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Link href="/dashboard/auditors/recovery-center">
            <Button size="sm">
              <RotateCcw className="h-4 w-4 mr-2" />
              Recovery Center
            </Button>
          </Link>
        </div>
      </DashboardHeader>

      <div className="space-y-6">
        {/* Error State */}
        {error && (
          <Card className="border-red-200 bg-red-50">
            <CardContent className="pt-6">
              <div className="flex items-center gap-2 text-red-600">
                <AlertTriangle className="h-4 w-4" />
                <span className="font-medium">Error loading deleted items</span>
              </div>
              <p className="text-sm text-red-600 mt-1">{error}</p>
              <Button variant="outline" size="sm" onClick={handleRefresh} className="mt-3">
                <RefreshCw className="h-4 w-4 mr-2" />
                Try Again
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Filters</CardTitle>
            <CardDescription>
              Filter deleted items by type, date, user, or status
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Search</label>
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search items..."
                    className="pl-9"
                    value={searchInput}
                    onChange={(e) => setSearchInput(e.target.value)}
                  />
                </div>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Item Type</label>
                <Select value={modelTypeFilter} onValueChange={handleModelTypeChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="All types" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    {modelTypes.map((type) => (
                      <SelectItem key={type} value={type}>
                        {type === 'Income' ? 'Income Transactions' :
                         type === 'Expenditure' ? 'Expenditure Records' :
                         type === 'Employee' ? 'Employee Records' :
                         type === 'Budget' ? 'Budget Items' :
                         type}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Review Status</label>
                <Select value={reviewStatusFilter} onValueChange={handleReviewStatusChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="All statuses" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    {reviewStatuses.map((status) => (
                      <SelectItem key={status.value} value={status.value}>
                        {status.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Fiscal Year</label>
                <Select value={fiscalYearFilter} onValueChange={handleFiscalYearChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="All years" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Years</SelectItem>
                    {fiscalYears.map((year) => (
                      <SelectItem key={year} value={year}>
                        {year}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Deleted Items Table */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Trash2 className="h-5 w-5 text-red-600" />
              Deleted Items ({totalCount})
            </CardTitle>
            <CardDescription>
              Complete audit trail of all deleted items with government compliance
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoadingItems ? (
              <div className="space-y-4">
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Item Details</TableHead>
                        <TableHead>Deleted By</TableHead>
                        <TableHead>Deletion Date</TableHead>
                        <TableHead>Review Status</TableHead>
                        <TableHead>Compliance</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {[...Array(5)].map((_, i) => (
                        <TableRow key={i}>
                          <TableCell>
                            <div className="space-y-2">
                              <Skeleton className="h-4 w-32" />
                              <Skeleton className="h-3 w-24" />
                              <Skeleton className="h-3 w-40" />
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="space-y-2">
                              <Skeleton className="h-4 w-24" />
                              <Skeleton className="h-3 w-20" />
                              <Skeleton className="h-3 w-32" />
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="space-y-2">
                              <Skeleton className="h-4 w-28" />
                              <Skeleton className="h-5 w-16" />
                            </div>
                          </TableCell>
                          <TableCell>
                            <Skeleton className="h-6 w-20" />
                          </TableCell>
                          <TableCell>
                            <div className="space-y-1">
                              <Skeleton className="h-5 w-16" />
                              <Skeleton className="h-5 w-20" />
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex gap-1">
                              <Skeleton className="h-8 w-8" />
                              <Skeleton className="h-8 w-8" />
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </div>
            ) : deletedItems && deletedItems.length > 0 ? (
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Item Details</TableHead>
                      <TableHead>Deleted By</TableHead>
                      <TableHead>Deletion Date</TableHead>
                      <TableHead>Review Status</TableHead>
                      <TableHead>Compliance</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {deletedItems.map((item) => {
                      const daysUntilExpiry = getDaysUntilExpiry(item.recoveryDeadline)
                      const complianceFlags = getComplianceFlags(item)
                      return (
                        <TableRow key={item.id}>
                          <TableCell>
                            <div className="space-y-1">
                              <p className="font-medium text-sm">{item.originalModel}</p>
                              <p className="text-xs text-muted-foreground">
                                ID: {item.originalId}
                              </p>
                              {item.originalData && (
                                <p className="text-xs text-gray-600">
                                  {item.originalData.reference || item.originalData.name || 'N/A'}
                                  {item.originalData.amount && ` - ${new Intl.NumberFormat('en-MW', { style: 'currency', currency: 'MWK' }).format(item.originalData.amount)}`}
                                </p>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="space-y-1">
                              <p className="font-medium text-sm">{item.deletedBy?.name || 'Unknown'}</p>
                              <p className="text-xs text-muted-foreground">{item.deletedBy?.role || 'N/A'}</p>
                              <p className="text-xs text-gray-600">{item.deletedBy?.email || 'N/A'}</p>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="space-y-1">
                              <p className="text-sm">{AuditDateFormatter.table(item.deletionDate)}</p>
                              <Badge variant="outline" className="text-xs">
                                {item.deletionType || 'single'}
                              </Badge>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="space-y-1">
                              {getStatusBadge(item.reviewStatus)}
                              {item.canBeRecovered && (
                                <p className="text-xs text-muted-foreground">
                                  {daysUntilExpiry > 0 ? `${daysUntilExpiry} days left` : "Expired"}
                                </p>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="space-y-1">
                              {complianceFlags.map((flag, index) => (
                                <Badge key={index} variant="secondary" className="text-xs mr-1">
                                  {flag}
                                </Badge>
                              ))}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-1">
                              <Button variant="ghost" size="sm" title="View Details">
                                <Eye className="h-4 w-4" />
                              </Button>
                              {item.canBeRecovered && daysUntilExpiry > 0 && (
                                <Button variant="ghost" size="sm" title="Recover Item">
                                  <RotateCcw className="h-4 w-4" />
                                </Button>
                              )}
                            </div>
                          </TableCell>
                        </TableRow>
                      )
                    })}
                  </TableBody>
                </Table>
              </div>
            ) : (
              <div className="text-center py-12 text-muted-foreground">
                <Database className="h-16 w-16 mx-auto mb-4 opacity-50" />
                <h3 className="text-lg font-medium mb-2">No deleted items found</h3>
                <p className="text-sm">No items match your current filters</p>
                <Button variant="outline" onClick={() => {
                  setSearchInput('');
                  setModelTypeFilter('all');
                  setReviewStatusFilter('all');
                  setFiscalYearFilter('all');
                  setFilters({});
                }} className="mt-4">
                  Clear Filters
                </Button>
              </div>
            )}

            {/* Pagination */}
            {!isLoadingItems && deletedItems && deletedItems.length > 0 && totalPages > 1 && (
              <div className="flex items-center justify-between mt-4 pt-4 border-t">
                <p className="text-sm text-muted-foreground">
                  Showing {((page - 1) * 20) + 1} to {Math.min(page * 20, totalCount)} of {totalCount} items
                </p>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setPage(page - 1)}
                    disabled={page <= 1}
                  >
                    Previous
                  </Button>
                  <span className="text-sm">
                    Page {page} of {totalPages}
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setPage(page + 1)}
                    disabled={page >= totalPages}
                  >
                    Next
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Compliance Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5 text-green-600" />
              Audit Compliance Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-3">
              <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
                <Clock className="h-5 w-5 text-blue-600" />
                <div>
                  <p className="font-medium text-sm">7-Year Retention</p>
                  <p className="text-xs text-muted-foreground">Government standard compliance</p>
                </div>
              </div>
              <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
                <Shield className="h-5 w-5 text-green-600" />
                <div>
                  <p className="font-medium text-sm">Secure Storage</p>
                  <p className="text-xs text-muted-foreground">Encrypted audit trail</p>
                </div>
              </div>
              <div className="flex items-center gap-3 p-3 bg-orange-50 rounded-lg">
                <RotateCcw className="h-5 w-5 text-orange-600" />
                <div>
                  <p className="font-medium text-sm">90-Day Recovery</p>
                  <p className="text-xs text-muted-foreground">Recovery window available</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardShell>
  )
}
