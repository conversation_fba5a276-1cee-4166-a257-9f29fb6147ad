'use client';

import { useQuery } from '@tanstack/react-query';
import { useBudget } from './use-budget';

export interface IncomeStats {
  totalIncome: number;
  actualIncome: number;
  budgetedIncome: number;
  plannedBudgetIncome: number; // From budget planning
  incomePercentage: number;
  governmentSubvention: number;
  registrationFees: number;
  licensingFees: number;
  donations: number;
  otherIncome: number;
  feeCollection: number;
  yearOverYearChange: number;
  budgetVariance: number;
  budgetVariancePercentage: number;
  actualBudgetVariance: number;
  actualBudgetVariancePercentage: number;
  trendDirection: 'up' | 'down' | 'neutral';
  incomeBySource: Array<{
    name: string;
    value: number;
    actualValue: number;
    budgetedValue: number;
    percentage: number;
    source: string;
    budgeted?: number;
    variance?: number;
    variancePercentage?: number;
  }>;
  monthlyIncome: Array<{
    month: string;
    government: number;
    registration: number;
    licensing: number;
    donations: number;
    other?: number;
    total: number;
    actualIncome: number;
    budgetedIncome: number;
    budgeted?: number;
  }>;
}

/**
 * Hook to calculate income statistics for dashboards and reports
 */
export function useIncomeStats(
  fiscalYear: string,
  budgetId?: string,
  options?: {
    refetchInterval?: number;
    refetchIntervalInBackground?: boolean;
    refetchOnWindowFocus?: boolean;
    staleTime?: number;
  }
) {
  const { getActiveBudget } = useBudget();

  // Default options
  const defaultOptions = {
    refetchInterval: false,
    refetchIntervalInBackground: false,
    refetchOnWindowFocus: true,
    staleTime: 5 * 60 * 1000, // 5 minutes
  };

  const queryOptions = { ...defaultOptions, ...options };

  // Fetch income summary data
  const {
    data: incomeSummary,
    isLoading: isLoadingIncomeSummary,
    refetch: refetchIncomeSummary
  } = useQuery({
    queryKey: ['income', 'summary', fiscalYear, budgetId],
    queryFn: async () => {
      // Build query string
      const queryParams = new URLSearchParams();
      if (fiscalYear) queryParams.append('fiscalYear', fiscalYear);
      if (budgetId) queryParams.append('budgetId', budgetId);

      const response = await fetch(`/api/accounting/income/summary?${queryParams.toString()}`);
      if (!response.ok) {
        throw new Error('Failed to fetch income summary');
      }

      return await response.json();
    },
    staleTime: queryOptions.staleTime,
    refetchInterval: queryOptions.refetchInterval,
    refetchIntervalInBackground: queryOptions.refetchIntervalInBackground,
    refetchOnWindowFocus: queryOptions.refetchOnWindowFocus,
  });

  // Fetch budget data for comparison
  const { data: budgetData, isLoading: isLoadingBudget } = useQuery({
    queryKey: ['budget', 'active', fiscalYear, budgetId],
    queryFn: async () => {
      if (budgetId) {
        const response = await fetch(`/api/accounting/budget/${budgetId}`);
        if (!response.ok) {
          throw new Error(`Failed to fetch budget with ID ${budgetId}`);
        }
        return await response.json();
      } else {
        // Get active budget for fiscal year
        const response = await fetch(`/api/accounting/budget/active?fiscalYear=${fiscalYear}`);
        if (!response.ok) {
          throw new Error('Failed to fetch active budget');
        }
        return await response.json();
      }
    },
    enabled: !!fiscalYear,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Fetch previous year's income for comparison
  const previousFiscalYear = getPreviousFiscalYear(fiscalYear);
  const { data: previousYearIncome, isLoading: isLoadingPreviousYear } = useQuery({
    queryKey: ['income', 'summary', previousFiscalYear],
    queryFn: async () => {
      const queryParams = new URLSearchParams({ fiscalYear: previousFiscalYear });
      const response = await fetch(`/api/accounting/income/summary?${queryParams.toString()}`);
      if (!response.ok) {
        throw new Error('Failed to fetch previous year income summary');
      }
      return await response.json();
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Fetch monthly income data
  const { data: monthlyData, isLoading: isLoadingMonthly } = useQuery({
    queryKey: ['income', 'monthly', fiscalYear, budgetId],
    queryFn: async () => {
      const queryParams = new URLSearchParams();
      if (fiscalYear) queryParams.append('fiscalYear', fiscalYear);
      if (budgetId) queryParams.append('budgetId', budgetId);
      
      const response = await fetch(`/api/accounting/income/monthly?${queryParams.toString()}`);
      if (!response.ok) {
        throw new Error('Failed to fetch monthly income data');
      }
      
      return await response.json();
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Calculate income statistics
  const calculateStats = (): IncomeStats | null => {
    if (isLoadingIncomeSummary || !incomeSummary) return null;

    // Extract income data with safe defaults
    const {
      incomeData = [],
      totalIncome = 0,
      actualIncome = 0,
      budgetedIncome = 0
    } = incomeSummary;

    // Get income by source
    const governmentSubvention = getIncomeBySource(incomeData, 'government_subvention');
    const registrationFees = getIncomeBySource(incomeData, 'registration_fees');
    const licensingFees = getIncomeBySource(incomeData, 'licensing_fees');
    const donations = getIncomeBySource(incomeData, 'donations');
    const otherIncome = getIncomeBySource(incomeData, 'other');
    const feeCollection = registrationFees + licensingFees;

    // Get planned budget income (from budget planning)
    const plannedBudgetIncome = budgetData?.budget?.totalIncome || 0;
    
    // Calculate budget variance (total vs planned budget)
    const budgetVariance = totalIncome - plannedBudgetIncome;
    const budgetVariancePercentage = plannedBudgetIncome > 0 ? (budgetVariance / plannedBudgetIncome) * 100 : 0;

    // Calculate actual budget variance (actual received vs planned budget)
    const actualBudgetVariance = actualIncome - plannedBudgetIncome;
    const actualBudgetVariancePercentage = plannedBudgetIncome > 0 ? (actualBudgetVariance / plannedBudgetIncome) * 100 : 0;

    // Calculate income percentage of budget (using actual income)
    const incomePercentage = plannedBudgetIncome > 0 ? (actualIncome / plannedBudgetIncome) * 100 : 0;
    
    // Calculate year-over-year change
    const previousYearTotal = previousYearIncome?.totalIncome || 0;
    const yearOverYearChange = previousYearTotal > 0 
      ? ((totalIncome - previousYearTotal) / previousYearTotal) * 100 
      : 0;
    
    // Determine trend direction
    const trendDirection = yearOverYearChange > 0 ? 'up' : yearOverYearChange < 0 ? 'down' : 'neutral';

    // Prepare income by source with budget comparison
    const incomeBySource = (incomeData || []).map((item: any) => {
      // Find matching budget category
      const budgetCategory = budgetData?.budget?.incomeCategories?.find(
        (cat: any) => cat.source === item.source
      );

      const budgeted = budgetCategory?.amount || 0;
      const variance = item.value - budgeted;
      const variancePercentage = budgeted > 0 ? (variance / budgeted) * 100 : 0;

      return {
        ...item,
        actualValue: item.actualValue || 0,
        budgetedValue: item.budgetedValue || 0,
        percentage: totalIncome > 0 ? (item.value / totalIncome) * 100 : 0,
        budgeted,
        variance,
        variancePercentage
      };
    });

    // Prepare monthly income data
    const monthlyIncome = monthlyData?.monthlyIncome || [];

    return {
      totalIncome,
      actualIncome,
      budgetedIncome,
      plannedBudgetIncome,
      incomePercentage,
      governmentSubvention,
      registrationFees,
      licensingFees,
      donations,
      otherIncome,
      feeCollection,
      yearOverYearChange,
      budgetVariance,
      budgetVariancePercentage,
      actualBudgetVariance,
      actualBudgetVariancePercentage,
      trendDirection,
      incomeBySource,
      monthlyIncome
    };
  };

  // Combined refetch function
  const refetch = async () => {
    await Promise.all([
      refetchIncomeSummary(),
      // Add other refetch functions when available
    ]);
  };

  return {
    incomeStats: calculateStats(),
    isLoading: isLoadingIncomeSummary || isLoadingBudget || isLoadingPreviousYear || isLoadingMonthly,
    incomeSummary,
    budgetData,
    previousYearIncome,
    monthlyData,
    refetch
  };
}

// Helper function to get income by source
function getIncomeBySource(incomeData: any[] | undefined, source: string): number {
  if (!incomeData || !Array.isArray(incomeData)) {
    return 0;
  }
  const item = incomeData.find(item => item.source === source);
  return item ? item.value : 0;
}

// Helper function to get previous fiscal year
function getPreviousFiscalYear(fiscalYear: string): string {
  const [startYear, endYear] = fiscalYear.split('-').map(Number);
  return `${startYear - 1}-${endYear - 1}`;
}
