// lib/stores/audit-store.ts
import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

export interface DeletedItem {
  id: string;
  originalId: string;
  originalModel: 'Income' | 'Expenditure' | 'Budget' | 'Employee' | 'Payroll' | 'Other';
  originalData: {
    reference?: string;
    name?: string;
    amount?: number;
    [key: string]: any;
  };
  deletedBy: {
    name: string;
    email: string;
    role: string;
  };
  deletionDate: string;
  deletedAt?: string; // Optional for compatibility with raw database responses
  deletionReason: string;
  deletionType: 'single' | 'bulk';
  recoveryDeadline: string;
  status: string;
  reviewStatus: 'pending' | 'approved' | 'flagged' | 'investigated';
  complianceFlags: string[];
  auditRecordId: string;
  canBeRecovered: boolean;
  fiscalYear?: string;
  auditNotes?: string;
  reviewedBy?: {
    name: string;
    email: string;
    role: string;
  };
  reviewedAt?: string;
}

export interface AuditStats {
  totalDeletedItems: number;
  pendingRecovery: number;
  complianceScore: number;
  lastAuditDate: string;
  retentionItems: number;
  criticalAlerts: number;
  nearingDeadline: number;
}

export interface RecentDeletion {
  id: string;
  itemType: string;
  deletedBy: string;
  deletionDate: string;
  reason: string;
  status: string;
  originalData: {
    reference: string;
    amount?: number;
  };
}

export interface AuditFilters {
  modelType?: string;
  startDate?: string;
  endDate?: string;
  deletedBy?: string;
  fiscalYear?: string;
  reviewStatus?: string;
  search?: string;
}

export interface AuditState {
  // Data
  deletedItems: DeletedItem[];
  auditStats: AuditStats | null;
  recentDeletions: RecentDeletion[];
  
  // Loading states
  isLoading: boolean;
  isLoadingStats: boolean;
  isLoadingItems: boolean;
  error: string | null;
  
  // Filters and pagination
  filters: AuditFilters;
  page: number;
  limit: number;
  totalCount: number;
  totalPages: number;
  
  // Selection
  selectedItems: string[];
  
  // Filter options
  fiscalYears: string[];
  modelTypes: string[];
  reviewStatuses: Array<{ value: string; label: string }>;
  
  // Actions
  setDeletedItems: (items: DeletedItem[]) => void;
  setAuditStats: (stats: AuditStats) => void;
  setRecentDeletions: (deletions: RecentDeletion[]) => void;
  setIsLoading: (loading: boolean) => void;
  setIsLoadingStats: (loading: boolean) => void;
  setIsLoadingItems: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setFilters: (filters: Partial<AuditFilters>) => void;
  setPage: (page: number) => void;
  setLimit: (limit: number) => void;
  setPagination: (pagination: { totalCount: number; totalPages: number }) => void;
  setSelectedItems: (items: string[]) => void;
  toggleItemSelection: (itemId: string) => void;
  selectAllItems: () => void;
  clearSelection: () => void;
  setFilterOptions: (options: {
    fiscalYears?: string[];
    modelTypes?: string[];
    reviewStatuses?: Array<{ value: string; label: string }>;
  }) => void;
  
  // API actions
  fetchDeletedItems: () => Promise<void>;
  fetchAuditStats: (fiscalYear?: string) => Promise<void>;
  updateReviewStatus: (ids: string[], reviewStatus: string, auditNotes?: string) => Promise<void>;
  refreshData: () => Promise<void>;
}

export const useAuditStore = create<AuditState>()(
  persist(
    immer((set, get) => ({
      // Initial state
      deletedItems: [],
      auditStats: null,
      recentDeletions: [],
      
      // Loading states
      isLoading: false,
      isLoadingStats: false,
      isLoadingItems: false,
      error: null,
      
      // Filters and pagination
      filters: {},
      page: 1,
      limit: 20,
      totalCount: 0,
      totalPages: 0,
      
      // Selection
      selectedItems: [],
      
      // Filter options
      fiscalYears: [],
      modelTypes: [],
      reviewStatuses: [],
      
      // Actions
      setDeletedItems: (items) => set((state) => {
        state.deletedItems = items;
      }),
      
      setAuditStats: (stats) => set((state) => {
        state.auditStats = stats;
      }),
      
      setRecentDeletions: (deletions) => set((state) => {
        state.recentDeletions = deletions;
      }),
      
      setIsLoading: (loading) => set((state) => {
        state.isLoading = loading;
      }),
      
      setIsLoadingStats: (loading) => set((state) => {
        state.isLoadingStats = loading;
      }),
      
      setIsLoadingItems: (loading) => set((state) => {
        state.isLoadingItems = loading;
      }),
      
      setError: (error) => set((state) => {
        state.error = error;
      }),
      
      setFilters: (filters) => set((state) => {
        state.filters = { ...state.filters, ...filters };
        state.page = 1; // Reset to first page when filters change
      }),
      
      setPage: (page) => set((state) => {
        state.page = page;
      }),
      
      setLimit: (limit) => set((state) => {
        state.limit = limit;
        state.page = 1; // Reset to first page when limit changes
      }),
      
      setPagination: (pagination) => set((state) => {
        state.totalCount = pagination.totalCount;
        state.totalPages = pagination.totalPages;
      }),
      
      setSelectedItems: (items) => set((state) => {
        state.selectedItems = items;
      }),
      
      toggleItemSelection: (itemId) => set((state) => {
        const index = state.selectedItems.indexOf(itemId);
        if (index > -1) {
          state.selectedItems.splice(index, 1);
        } else {
          state.selectedItems.push(itemId);
        }
      }),
      
      selectAllItems: () => set((state) => {
        state.selectedItems = state.deletedItems.map(item => item.id);
      }),
      
      clearSelection: () => set((state) => {
        state.selectedItems = [];
      }),
      
      setFilterOptions: (options) => set((state) => {
        if (options.fiscalYears) state.fiscalYears = options.fiscalYears;
        if (options.modelTypes) state.modelTypes = options.modelTypes;
        if (options.reviewStatuses) state.reviewStatuses = options.reviewStatuses;
      }),
      
      // API actions
      fetchDeletedItems: async () => {
        const state = get();
        set((draft) => {
          draft.isLoadingItems = true;
          draft.error = null;
        });
        
        try {
          const params = new URLSearchParams({
            page: state.page.toString(),
            limit: state.limit.toString(),
            ...state.filters
          });
          
          const response = await fetch(`/api/audit/deleted-items?${params}`);
          if (!response.ok) {
            throw new Error('Failed to fetch deleted items');
          }
          
          const data = await response.json();
          
          set((draft) => {
            draft.deletedItems = data.deletedItems;
            draft.totalCount = data.pagination.totalCount;
            draft.totalPages = data.pagination.totalPages;
            if (data.filters) {
              draft.fiscalYears = data.filters.fiscalYears || [];
              draft.modelTypes = data.filters.modelTypes || [];
              draft.reviewStatuses = data.filters.reviewStatuses || [];
            }
            draft.isLoadingItems = false;
          });
        } catch (error) {
          set((draft) => {
            draft.error = error instanceof Error ? error.message : 'Failed to fetch deleted items';
            draft.isLoadingItems = false;
          });
        }
      },
      
      fetchAuditStats: async (fiscalYear) => {
        set((draft) => {
          draft.isLoadingStats = true;
          draft.error = null;
        });
        
        try {
          const params = new URLSearchParams();
          if (fiscalYear) params.append('fiscalYear', fiscalYear);
          
          const response = await fetch(`/api/audit/stats?${params}`);
          if (!response.ok) {
            throw new Error('Failed to fetch audit stats');
          }
          
          const data = await response.json();
          
          set((draft) => {
            draft.auditStats = data.stats;
            draft.recentDeletions = data.recentDeletions;
            draft.isLoadingStats = false;
          });
        } catch (error) {
          set((draft) => {
            draft.error = error instanceof Error ? error.message : 'Failed to fetch audit stats';
            draft.isLoadingStats = false;
          });
        }
      },
      
      updateReviewStatus: async (ids, reviewStatus, auditNotes) => {
        set((draft) => {
          draft.isLoading = true;
          draft.error = null;
        });
        
        try {
          const response = await fetch('/api/audit/deleted-items', {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ ids, reviewStatus, auditNotes })
          });
          
          if (!response.ok) {
            throw new Error('Failed to update review status');
          }
          
          // Refresh deleted items after update
          await get().fetchDeletedItems();
          
          set((draft) => {
            draft.selectedItems = [];
            draft.isLoading = false;
          });
        } catch (error) {
          set((draft) => {
            draft.error = error instanceof Error ? error.message : 'Failed to update review status';
            draft.isLoading = false;
          });
        }
      },
      
      refreshData: async () => {
        const state = get();
        await Promise.all([
          state.fetchDeletedItems(),
          state.fetchAuditStats()
        ]);
      }
    })),
    {
      name: 'audit-store',
      partialize: (state) => ({
        filters: state.filters,
        page: state.page,
        limit: state.limit
      })
    }
  )
);
