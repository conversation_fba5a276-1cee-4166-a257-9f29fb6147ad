// scripts/test-audit-dates.js
// Quick script to test audit date formatting

const { AuditDateFormatter } = require('../lib/utils/date-formatter');

// Test various date inputs
const testDates = [
  new Date(), // Current date
  new Date('2025-01-15T10:30:00Z'), // ISO string
  '2025-01-14T14:20:00Z', // String
  1737024000000, // Timestamp
  null, // Null
  undefined, // Undefined
  'invalid-date', // Invalid string
  { $date: { $numberLong: '1737024000000' } }, // MongoDB format
];

console.log('🧪 Testing Audit Date Formatter\n');

testDates.forEach((date, index) => {
  console.log(`Test ${index + 1}: ${JSON.stringify(date)}`);
  console.log(`  Table:     ${AuditDateFormatter.table(date)}`);
  console.log(`  Dashboard: ${AuditDateFormatter.dashboard(date)}`);
  console.log(`  Report:    ${AuditDateFormatter.report(date)}`);
  console.log(`  Relative:  ${AuditDateFormatter.relative(date)}`);
  console.log(`  Days Until: ${AuditDateFormatter.daysUntil(date)}`);
  console.log(`  Valid:     ${AuditDateFormatter.isValid(date)}`);
  console.log(`  Fiscal:    ${AuditDateFormatter.fiscalYear(date)}`);
  console.log('');
});

console.log('✅ Date formatting tests completed');
console.log('\n📋 Usage in components:');
console.log('- Table dates: AuditDateFormatter.table(item.deletionDate)');
console.log('- Dashboard: AuditDateFormatter.dashboard(item.deletionDate)');
console.log('- Reports: AuditDateFormatter.report(item.deletionDate)');
console.log('- Relative: AuditDateFormatter.relative(item.deletionDate)');
console.log('- Days until deadline: AuditDateFormatter.daysUntil(item.recoveryDeadline)');
