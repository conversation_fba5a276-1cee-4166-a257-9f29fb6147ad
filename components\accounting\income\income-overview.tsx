'use client';

import React, { useState, useMemo, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import {
  RefreshCw,
  Clock,
  DollarSign,
  TrendingUp,
  TrendingDown,
  Target,
  Calendar,
  AlertTriangle,
  CheckCircle,
  ArrowUpRight,
  ArrowDownRight,
  BarChart4,
  Activity,
  Zap
} from 'lucide-react';
import { useIncomeStats } from '@/lib/hooks/accounting/use-income-stats';
import { useBudget } from '@/lib/hooks/accounting/use-budget';
import { useIsMobile } from '@/lib/hooks/use-responsive';
import { DateRange } from 'react-day-picker';
import { DateRangePicker } from '@/components/accounting/shared/date-range-picker';
import { IncomeSourcesChart } from '@/components/accounting/income/income-sources-chart';
import { IncomeTable } from '@/components/accounting/income/income-table';
import { IncomeEmptyState } from '@/components/accounting/income/income-empty-state';
import { formatCurrency } from '@/lib/utils/currency';
import { Income } from '@/types/accounting';
import { useIncomeStore } from '@/lib/stores/enhanced-income-store';

// Utility functions
const formatCompactCurrency = (value: number) => {
  if (value >= **********) {
    return `MWK ${(value / **********).toFixed(1)}B`;
  } else if (value >= 1000000) {
    return `MWK ${(value / 1000000).toFixed(1)}M`;
  } else if (value >= 1000) {
    return `MWK ${(value / 1000).toFixed(1)}K`;
  }
  return formatCurrency(value);
};

interface IncomeOverviewProps {
  onEditIncome?: (income: Income) => void;
  onDeleteIncome?: (income: Income) => void;
}

export function IncomeOverview({ onEditIncome, onDeleteIncome }: IncomeOverviewProps) {
  const [lastRefresh, setLastRefresh] = useState(new Date());
  const [activeBudgetId, setActiveBudgetId] = useState<string | null>(null);
  const [dateRange, setDateRange] = useState<DateRange | undefined>(undefined);
  const [loadingTimeout, setLoadingTimeout] = useState(false);

  const isMobile = useIsMobile();

  // Use enhanced income store for fiscal year management
  const { getCurrentFiscalYear, getActiveFiscalYears, initializeFormData, isFormDataReady } = useIncomeStore();
  const [fiscalYear, setFiscalYear] = useState(() => getCurrentFiscalYear());

  // Initialize store data on component mount
  React.useEffect(() => {
    if (!isFormDataReady) {
      initializeFormData().catch(console.error);
    }
  }, [isFormDataReady, initializeFormData]);

  // Auto-refresh interval (10 minutes = 600,000 ms)
  const REFRESH_INTERVAL = 10 * 60 * 1000;

  // Get budget data
  const { activeBudgets } = useBudget();

  // Get active budget for the fiscal year
  const activeBudget = useMemo(() => {
    if (!activeBudgets?.length) return null;
    return activeBudgets.find((budget: any) =>
      budget.fiscalYear === fiscalYear &&
      (budget.status === 'active' || budget.status === 'approved')
    ) || activeBudgets[0];
  }, [activeBudgets, fiscalYear]);

  // Update active budget ID when budget changes
  React.useEffect(() => {
    if (activeBudget?.id && activeBudget.id !== activeBudgetId) {
      setActiveBudgetId(activeBudget.id);
    }
  }, [activeBudget, activeBudgetId]);

  // Get income statistics with auto-refetch
  const {
    incomeStats,
    isLoading: isLoadingIncomeStats,
    refetch: refetchIncomeStats
  } = useIncomeStats(fiscalYear, activeBudgetId || undefined, {
    refetchInterval: REFRESH_INTERVAL,
    refetchIntervalInBackground: true,
    refetchOnWindowFocus: true,
    staleTime: 5 * 60 * 1000, // Consider data stale after 5 minutes
  });

  // Debug income stats
  React.useEffect(() => {
    console.log('IncomeOverview: Income stats updated:', {
      fiscalYear,
      activeBudgetId,
      incomeStats,
      isLoading: isLoadingIncomeStats
    });
  }, [fiscalYear, activeBudgetId, incomeStats, isLoadingIncomeStats]);

  // Loading timeout handler
  React.useEffect(() => {
    let timeout: NodeJS.Timeout;

    if (isLoadingIncomeStats) {
      timeout = setTimeout(() => {
        setLoadingTimeout(true);
      }, 15000); // 15 second timeout
    } else {
      setLoadingTimeout(false);
    }

    return () => {
      if (timeout) {
        clearTimeout(timeout);
      }
    };
  }, [isLoadingIncomeStats]);

  // Manual refresh function
  const handleManualRefresh = useCallback(() => {
    console.log('IncomeOverview: Manual refresh triggered');
    if (refetchIncomeStats) {
      refetchIncomeStats();
      setLastRefresh(new Date());
    }
  }, [refetchIncomeStats]);

  // Handle fiscal year change
  const handleFiscalYearChange = useCallback((newFiscalYear: string) => {
    if (newFiscalYear !== fiscalYear) {
      console.log('IncomeOverview: Fiscal year changing from', fiscalYear, 'to', newFiscalYear);
      setFiscalYear(newFiscalYear);
    }
  }, [fiscalYear]);

  // Handle date range change
  const handleDateRangeChange = useCallback((range: DateRange | undefined) => {
    setDateRange(range);
  }, []);

  // Calculate KPIs and metrics
  const kpis = useMemo(() => {
    if (!incomeStats) return null;

    // Use actual income (received) for budget performance calculations
    const actualTotal = incomeStats.actualIncome || 0;
    const budgetedTotal = incomeStats.budgetedIncome || 0; // Approved income (committed)
    const plannedBudgetTotal = incomeStats.plannedBudgetIncome || 0; // From budget planning
    const totalIncome = incomeStats.totalIncome || 0; // Both approved + received
    const previousTotal = 0; // Will be calculated from previous year data when available

    // Budget variance calculations
    const budgetVariance = actualTotal - plannedBudgetTotal; // Actual vs planned budget
    const budgetVariancePercentage = plannedBudgetTotal > 0 ? (budgetVariance / plannedBudgetTotal) * 100 : 0;
    const periodChange = incomeStats.yearOverYearChange || 0;

    const averageMonthlyIncome = incomeStats.monthlyIncome
      ? incomeStats.monthlyIncome.reduce((sum, month) => sum + month.total, 0) / incomeStats.monthlyIncome.length
      : 0;

    // Budget utilization based on actual income vs planned budget
    const budgetUtilization = plannedBudgetTotal > 0 ? (actualTotal / plannedBudgetTotal) * 100 : 0;

    return {
      totalIncome,
      actualIncome: actualTotal,
      budgetedIncome: budgetedTotal,
      plannedBudgetIncome: plannedBudgetTotal,
      previousPeriodIncome: previousTotal,
      budgetVariance,
      budgetVariancePercentage,
      periodChange,
      averageMonthlyIncome,
      budgetUtilization,
      isOverBudget: budgetUtilization > 100,
      isOnTrack: budgetUtilization >= 80 && budgetUtilization <= 100,
      needsAttention: budgetUtilization < 50
    };
  }, [incomeStats]);

  // Handle loading timeout
  if (loadingTimeout) {
    return (
      <IncomeEmptyState
        type="loading-timeout"
        title="Income Overview Loading Timeout"
        description="The income overview is taking longer than expected to load."
        onRetry={handleManualRefresh}
      />
    );
  }

  // Handle loading state
  if (isLoadingIncomeStats && !loadingTimeout) {
    return (
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <Skeleton className="h-8 w-48" />
          <div className="flex gap-2">
            <Skeleton className="h-9 w-32" />
            <Skeleton className="h-9 w-24" />
          </div>
        </div>

        {/* KPI Cards Skeleton */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <Card key={i}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-4 w-4" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-32 mb-2" />
                <Skeleton className="h-3 w-20" />
              </CardContent>
            </Card>
          ))}
        </div>

        <Skeleton className="h-96 w-full" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Controls */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Income Overview</h2>
          <p className="text-muted-foreground">
            Track and analyze income performance for {fiscalYear}
          </p>
        </div>
        <div className="flex flex-wrap items-center gap-2">
          <Select value={fiscalYear} onValueChange={handleFiscalYearChange}>
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="Select fiscal year" />
            </SelectTrigger>
            <SelectContent>
              {getActiveFiscalYears().length > 0 ? (
                getActiveFiscalYears().map(fy => (
                  <SelectItem key={fy.year} value={fy.year}>
                    {fy.year} {fy.isCurrent ? '(Current)' : ''}
                  </SelectItem>
                ))
              ) : (
                <SelectItem value={fiscalYear} disabled>
                  {fiscalYear} (Default)
                </SelectItem>
              )}
            </SelectContent>
          </Select>

          <DateRangePicker
            dateRange={dateRange}
            onDateRangeChange={handleDateRangeChange}
            className="hidden md:block"
          />

          <Button
            variant="outline"
            size="sm"
            onClick={handleManualRefresh}
            disabled={isLoadingIncomeStats}
          >
            <RefreshCw className={`mr-2 h-4 w-4 ${isLoadingIncomeStats ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* KPI Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {/* Total Income */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Income</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {isMobile
                ? formatCompactCurrency(kpis?.totalIncome || 0)
                : formatCurrency(kpis?.totalIncome || 0)
              }
            </div>
            <div className="flex items-center text-xs text-muted-foreground">
              {kpis?.periodChange !== undefined && (
                <>
                  {kpis.periodChange >= 0 ? (
                    <ArrowUpRight className="mr-1 h-3 w-3 text-green-500" />
                  ) : (
                    <ArrowDownRight className="mr-1 h-3 w-3 text-red-500" />
                  )}
                  <span className={kpis.periodChange >= 0 ? 'text-green-600' : 'text-red-600'}>
                    {Math.abs(kpis.periodChange).toFixed(1)}% from last period
                  </span>
                </>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Budget Performance */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Budget Performance</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {kpis?.budgetUtilization.toFixed(1)}%
            </div>
            <Progress
              value={Math.min(kpis?.budgetUtilization || 0, 100)}
              className="mt-2"
            />
            <div className="flex items-center justify-between text-xs text-muted-foreground mt-1">
              <span>
                {isMobile
                  ? formatCompactCurrency(kpis?.actualIncome || 0)
                  : formatCurrency(kpis?.actualIncome || 0)
                }
              </span>
              <span>
                {isMobile
                  ? formatCompactCurrency(kpis?.plannedBudgetIncome || 0)
                  : formatCurrency(kpis?.plannedBudgetIncome || 0)
                }
              </span>
            </div>
          </CardContent>
        </Card>

        {/* Budget Variance */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Budget Variance</CardTitle>
            {kpis?.budgetVariance && kpis.budgetVariance >= 0 ? (
              <TrendingUp className="h-4 w-4 text-green-500" />
            ) : (
              <TrendingDown className="h-4 w-4 text-red-500" />
            )}
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${
              kpis?.budgetVariance && kpis.budgetVariance >= 0 ? 'text-green-600' : 'text-red-600'
            }`}>
              {kpis?.budgetVariance && kpis.budgetVariance >= 0 ? '+' : ''}
              {isMobile
                ? formatCompactCurrency(kpis?.budgetVariance || 0)
                : formatCurrency(kpis?.budgetVariance || 0)
              }
            </div>
            <div className="flex items-center text-xs text-muted-foreground">
              <span>
                {kpis?.budgetVariancePercentage && kpis.budgetVariancePercentage >= 0 ? '+' : ''}
                {kpis?.budgetVariancePercentage.toFixed(1)}% vs budget
              </span>
            </div>
          </CardContent>
        </Card>

        {/* Average Monthly */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Monthly Income</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {isMobile
                ? formatCompactCurrency(kpis?.averageMonthlyIncome || 0)
                : formatCurrency(kpis?.averageMonthlyIncome || 0)
              }
            </div>
            <div className="flex items-center text-xs text-muted-foreground">
              <Calendar className="mr-1 h-3 w-3" />
              <span>Based on {incomeStats?.monthlyIncome?.length || 0} months</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tabs for Different Views */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="sources">Income Sources</TabsTrigger>
          <TabsTrigger value="transactions">Transactions</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            {/* Income Sources Chart */}
            <Card>
              <CardHeader>
                <CardTitle>Income by Source</CardTitle>
                <CardDescription>
                  Distribution of income across different sources
                </CardDescription>
              </CardHeader>
              <CardContent>
                <IncomeSourcesChart
                  fiscalYear={fiscalYear}
                  budgetId={activeBudgetId || undefined}
                  showFilters={false}
                  height={300}
                />
              </CardContent>
            </Card>

            {/* Budget Status */}
            <Card>
              <CardHeader>
                <CardTitle>Budget Status</CardTitle>
                <CardDescription>
                  Current budget performance and alerts
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {/* Budget Progress */}
                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium">Budget Utilization</span>
                      <span className="text-sm text-muted-foreground">
                        {kpis?.budgetUtilization.toFixed(1)}%
                      </span>
                    </div>
                    <Progress value={Math.min(kpis?.budgetUtilization || 0, 100)} />
                  </div>

                  {/* Status Alerts */}
                  <div className="space-y-3">
                    {kpis?.isOverBudget && (
                      <div className="flex items-start space-x-3 p-3 bg-red-50 border border-red-200 rounded-lg">
                        <AlertTriangle className="h-5 w-5 text-red-500 mt-0.5" />
                        <div>
                          <p className="font-medium text-red-800">Over Budget</p>
                          <p className="text-sm text-red-600">
                            Income has exceeded the allocated budget by{' '}
                            {formatCurrency(Math.abs(kpis.budgetVariance))}
                          </p>
                        </div>
                      </div>
                    )}

                    {kpis?.isOnTrack && (
                      <div className="flex items-start space-x-3 p-3 bg-green-50 border border-green-200 rounded-lg">
                        <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                        <div>
                          <p className="font-medium text-green-800">On Track</p>
                          <p className="text-sm text-green-600">
                            Income performance is within expected budget range
                          </p>
                        </div>
                      </div>
                    )}

                    {kpis?.needsAttention && (
                      <div className="flex items-start space-x-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                        <Clock className="h-5 w-5 text-yellow-500 mt-0.5" />
                        <div>
                          <p className="font-medium text-yellow-800">Needs Attention</p>
                          <p className="text-sm text-yellow-600">
                            Income is significantly below budget expectations
                          </p>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Quick Stats */}
                  <div className="grid grid-cols-3 gap-4 pt-4 border-t">
                    <div className="text-center">
                      <div className="text-lg font-semibold">
                        {formatCompactCurrency(kpis?.actualIncome || 0)}
                      </div>
                      <div className="text-xs text-muted-foreground">Actual</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-semibold">
                        {formatCompactCurrency(kpis?.budgetedIncome || 0)}
                      </div>
                      <div className="text-xs text-muted-foreground">Approved</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-semibold">
                        {formatCompactCurrency(kpis?.plannedBudgetIncome || 0)}
                      </div>
                      <div className="text-xs text-muted-foreground">Planned</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Recent Transactions */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Income Transactions</CardTitle>
              <CardDescription>
                Latest income entries for {fiscalYear}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <IncomeTable
                fiscalYear={fiscalYear}
                budgetId={activeBudgetId || undefined}
                showFilters={false}
                limit={5}
                onEditIncome={onEditIncome}
                onDeleteIncome={onDeleteIncome}
              />
            </CardContent>
          </Card>
        </TabsContent>

        {/* Income Sources Tab */}
        <TabsContent value="sources" className="space-y-4">
          <IncomeSourcesChart
            fiscalYear={fiscalYear}
            budgetId={activeBudgetId || undefined}
            showFilters={true}
            height={400}
          />
        </TabsContent>

        {/* Transactions Tab */}
        <TabsContent value="transactions" className="space-y-4">
          <IncomeTable
            fiscalYear={fiscalYear}
            budgetId={activeBudgetId || undefined}
            showFilters={true}
            limit={20}
          />
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value="analytics" className="space-y-4">
          <div className="grid gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Performance Analytics</CardTitle>
                <CardDescription>
                  Detailed income performance analysis and trends
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <BarChart4 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">Advanced Analytics</h3>
                  <p className="text-muted-foreground mb-4">
                    Detailed analytics and forecasting features coming soon.
                  </p>
                  <Badge variant="secondary">
                    <Zap className="mr-1 h-3 w-3" />
                    Auto-refresh: Every 10 minutes
                  </Badge>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Footer Info */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between text-sm text-muted-foreground">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-1">
                <Clock className="h-3 w-3" />
                <span>Last updated: {lastRefresh.toLocaleTimeString()}</span>
              </div>
              <div className="flex items-center gap-1">
                <RefreshCw className="h-3 w-3" />
                <span>Auto-refresh: Every 10 minutes</span>
              </div>
            </div>
            <div className="flex items-center gap-1">
              <Activity className="h-3 w-3" />
              <span>Fiscal Year: {fiscalYear}</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
