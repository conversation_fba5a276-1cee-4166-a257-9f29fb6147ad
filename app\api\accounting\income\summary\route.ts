// app/api/accounting/income/summary/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { connectToDatabase } from '@/lib/database';
import Income from '@/models/accounting/Income';
import Budget from '@/models/accounting/Budget';
import mongoose from 'mongoose';

export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.FINANCE_OFFICER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const fiscalYear = searchParams.get('fiscalYear') || '2024-2025';
    const budgetId = searchParams.get('budgetId');

    // Connect to database
    await connectToDatabase();

    // Build base filter for income aggregation
    const baseFilter: Record<string, any> = {
      fiscalYear,
      appliedToBudget: true
    };

    // Add budget filter if provided
    if (budgetId) {
      baseFilter.budget = new mongoose.Types.ObjectId(budgetId);
    }

    // Get actual income (received only) - this is what was physically received
    const actualIncomeResult = await Income.aggregate([
      { $match: { ...baseFilter, status: 'received' } },
      { $group: { _id: null, total: { $sum: '$amount' } } }
    ]);

    // Get budgeted income (approved only) - this is committed but not yet received
    const budgetedIncomeResult = await Income.aggregate([
      { $match: { ...baseFilter, status: 'approved' } },
      { $group: { _id: null, total: { $sum: '$amount' } } }
    ]);

    // Get total income (both approved and received) for compatibility
    const totalIncomeResult = await Income.aggregate([
      { $match: { ...baseFilter, status: { $in: ['received', 'approved'] } } },
      { $group: { _id: null, total: { $sum: '$amount' } } }
    ]);

    const actualIncome = actualIncomeResult[0]?.total || 0;
    const budgetedIncome = budgetedIncomeResult[0]?.total || 0;
    const totalIncome = totalIncomeResult[0]?.total || 0;

    // Get income by source (both approved and received for source breakdown)
    const incomeBySource = await Income.aggregate([
      { $match: { ...baseFilter, status: { $in: ['received', 'approved'] } } },
      {
        $group: {
          _id: '$source',
          value: { $sum: '$amount' },
          count: { $sum: 1 },
          actualValue: {
            $sum: {
              $cond: [{ $eq: ['$status', 'received'] }, '$amount', 0]
            }
          },
          budgetedValue: {
            $sum: {
              $cond: [{ $eq: ['$status', 'approved'] }, '$amount', 0]
            }
          }
        }
      }
    ]);

    // Format income data
    const incomeData = incomeBySource.map(item => ({
      source: item._id,
      value: item.value,
      actualValue: item.actualValue,
      budgetedValue: item.budgetedValue,
      description: item._id === 'government_subvention' ? 'Government Subvention' :
        item._id === 'registration_fees' ? 'Registration Fees' :
        item._id === 'licensing_fees' ? 'Licensing Fees' :
        item._id === 'donations' ? 'Donations and Grants' : 'Other Income'
    }));

    // Get budget data for comparison
    let totalBudgeted = 0;
    let budgetData: any = null;

    if (budgetId) {
      budgetData = await Budget.findById(budgetId)
        .populate({
          path: 'categories',
          match: { type: 'income' },
          select: 'name budgetedAmount'
        });

      if (budgetData && budgetData.categories) {
        totalBudgeted = budgetData.categories.reduce((sum: number, cat: any) => sum + (cat.budgetedAmount || 0), 0);
      }
    } else {
      // Get active budget for fiscal year
      const activeBudget = await Budget.findOne({
        fiscalYear,
        status: { $in: ['active', 'approved'] }
      }).populate({
        path: 'categories',
        match: { type: 'income' },
        select: 'name budgetedAmount'
      });

      if (activeBudget && activeBudget.categories) {
        totalBudgeted = activeBudget.categories.reduce((sum: number, cat: any) => sum + (cat.budgetedAmount || 0), 0);
        budgetData = activeBudget;
      }
    }

    // Calculate percentage of budget
    const percentageOfBudget = totalBudgeted > 0 ? (totalIncome / totalBudgeted) * 100 : 0;

    // Get monthly income data (both actual and budgeted)
    const monthlyIncomeData = await Income.aggregate([
      { $match: { ...baseFilter, status: { $in: ['received', 'approved'] } } },
      {
        $group: {
          _id: {
            year: { $year: '$date' },
            month: { $month: '$date' }
          },
          income: { $sum: '$amount' },
          actualIncome: {
            $sum: {
              $cond: [{ $eq: ['$status', 'received'] }, '$amount', 0]
            }
          },
          budgetedIncome: {
            $sum: {
              $cond: [{ $eq: ['$status', 'approved'] }, '$amount', 0]
            }
          }
        }
      },
      { $sort: { '_id.year': 1, '_id.month': 1 } }
    ]);

    // Format monthly data with proper month names
    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    const monthlyData = monthlyIncomeData.map(item => ({
      month: monthNames[item._id.month - 1],
      income: item.income,
      actualIncome: item.actualIncome,
      budgetedIncome: item.budgetedIncome,
      budgeted: totalBudgeted > 0 ? Math.round(totalBudgeted / 12) : 0
    }));

    // Calculate quarterly data
    const quarterlyData = [];
    for (let q = 1; q <= 4; q++) {
      const quarterMonths = monthlyData.slice((q - 1) * 3, q * 3);
      const quarterIncome = quarterMonths.reduce((sum, month) => sum + month.income, 0);
      const quarterBudgeted = quarterMonths.reduce((sum, month) => sum + month.budgeted, 0);

      quarterlyData.push({
        quarter: `Q${q}`,
        income: quarterIncome,
        budgeted: quarterBudgeted
      });
    }

    const responseData = {
      fiscalYear,
      totalIncome,
      actualIncome,
      budgetedIncome,
      totalBudgeted,
      percentageOfBudget: Math.round(percentageOfBudget * 100) / 100,
      incomeData,
      monthlyData,
      quarterlyData
    };

    return NextResponse.json(responseData);
  } catch (error) {
    console.error('Error fetching income summary:', error);
    return NextResponse.json(
      { error: 'An error occurred while fetching income summary' },
      { status: 500 }
    );
  }
}
