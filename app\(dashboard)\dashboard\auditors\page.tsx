// app/(dashboard)/dashboard/auditors/page.tsx
"use client"

import { useEffect } from "react"
import { DashboardShell } from "@/components/dashboard-shell"
import { DashboardHeader } from "@/components/dashboard-header"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { Skeleton } from "@/components/ui/skeleton"
import {
  FileSearch,
  Trash2,
  FileText,
  BarChart3,
  RotateCcw,
  Settings,
  Shield,
  Clock,
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  Database,
  Users,
  Calendar,
  RefreshCw
} from "lucide-react"
import Link from "next/link"
import { useAuditStore } from "@/lib/stores/audit-store"
import { format } from "date-fns"

export default function AuditorsPage() {
  const {
    auditStats,
    recentDeletions,
    isLoadingStats,
    error,
    fetchAuditStats
  } = useAuditStore();

  // Fetch data on component mount
  useEffect(() => {
    fetchAuditStats();
  }, [fetchAuditStats]);

  // Handle refresh
  const handleRefresh = () => {
    fetchAuditStats();
  };

  // Safe date formatting function
  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A';
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return 'Invalid Date';
      return format(date, 'MMM dd, yyyy');
    } catch (error) {
      return 'Invalid Date';
    }
  };

  const quickActions = [
    {
      title: "View Deleted Items",
      description: "Browse all deleted items with full audit trail",
      href: "/dashboard/auditors/deleted-items",
      icon: Trash2,
      color: "bg-red-50 text-red-600 border-red-200"
    },
    {
      title: "Audit Trail",
      description: "Complete audit trail of all system activities",
      href: "/dashboard/auditors/audit-trail",
      icon: FileText,
      color: "bg-blue-50 text-blue-600 border-blue-200"
    },
    {
      title: "Compliance Reports",
      description: "Generate compliance reports for government auditing",
      href: "/dashboard/auditors/compliance-reports",
      icon: BarChart3,
      color: "bg-green-50 text-green-600 border-green-200"
    },
    {
      title: "Recovery Center",
      description: "Recover deleted items within 90-day window",
      href: "/dashboard/auditors/recovery-center",
      icon: RotateCcw,
      color: "bg-orange-50 text-orange-600 border-orange-200"
    }
  ]

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Auditors Dashboard"
        text="Government audit compliance and deleted items management for Teachers Council of Malawi"
      >
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="text-green-600 border-green-200">
            <Shield className="h-3 w-3 mr-1" />
            Government Compliant
          </Badge>
          <Button variant="outline" size="sm" onClick={handleRefresh} disabled={isLoadingStats}>
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoadingStats ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Link href="/dashboard/auditors/settings">
            <Button variant="outline" size="sm">
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Button>
          </Link>
        </div>
      </DashboardHeader>

      <div className="space-y-6">
        {/* Error State */}
        {error && (
          <Card className="border-red-200 bg-red-50">
            <CardContent className="pt-6">
              <div className="flex items-center gap-2 text-red-600">
                <AlertTriangle className="h-4 w-4" />
                <span className="font-medium">Error loading audit data</span>
              </div>
              <p className="text-sm text-red-600 mt-1">{error}</p>
              <Button variant="outline" size="sm" onClick={handleRefresh} className="mt-3">
                <RefreshCw className="h-4 w-4 mr-2" />
                Try Again
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Statistics Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Deleted Items</CardTitle>
              <Database className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              {isLoadingStats ? (
                <Skeleton className="h-8 w-24" />
              ) : (
                <div className="text-2xl font-bold">{auditStats?.totalDeletedItems?.toLocaleString() || 0}</div>
              )}
              <p className="text-xs text-muted-foreground">
                7-year retention period
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending Recovery</CardTitle>
              <RotateCcw className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              {isLoadingStats ? (
                <Skeleton className="h-8 w-16" />
              ) : (
                <div className="text-2xl font-bold text-orange-600">{auditStats?.pendingRecovery || 0}</div>
              )}
              <p className="text-xs text-muted-foreground">
                Items within recovery window
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Compliance Score</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              {isLoadingStats ? (
                <Skeleton className="h-8 w-20" />
              ) : (
                <div className="text-2xl font-bold text-green-600">{auditStats?.complianceScore || 0}%</div>
              )}
              <p className="text-xs text-muted-foreground">
                Government standards met
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Critical Alerts</CardTitle>
              <AlertTriangle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              {isLoadingStats ? (
                <Skeleton className="h-8 w-12" />
              ) : (
                <div className="text-2xl font-bold text-red-600">{auditStats?.criticalAlerts || 0}</div>
              )}
              <p className="text-xs text-muted-foreground">
                Require immediate attention
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>
              Access key audit and compliance functions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              {quickActions.map((action) => (
                <Link key={action.href} href={action.href}>
                  <Card className={`cursor-pointer transition-colors hover:bg-accent ${action.color} border-2`}>
                    <CardContent className="p-4">
                      <div className="flex items-center gap-3">
                        <action.icon className="h-6 w-6" />
                        <div>
                          <h3 className="font-medium text-sm">{action.title}</h3>
                          <p className="text-xs opacity-80">{action.description}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </Link>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Recent Deletions */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Deletions</CardTitle>
            <CardDescription>
              Latest items moved to audit trail
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoadingStats ? (
              <div className="space-y-4">
                {[...Array(3)].map((_, i) => (
                  <div key={i} className="flex items-center gap-3">
                    <Skeleton className="h-10 w-10 rounded-full" />
                    <div className="space-y-2 flex-1">
                      <Skeleton className="h-4 w-48" />
                      <Skeleton className="h-3 w-64" />
                      <Skeleton className="h-3 w-80" />
                    </div>
                    <Skeleton className="h-6 w-20" />
                  </div>
                ))}
              </div>
            ) : recentDeletions && recentDeletions.length > 0 ? (
              <div className="space-y-4">
                {recentDeletions.map((deletion, index) => (
                  <div key={deletion.id}>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="p-2 bg-red-50 rounded-full">
                          <Trash2 className="h-4 w-4 text-red-600" />
                        </div>
                        <div>
                          <p className="font-medium text-sm">{deletion.itemType}</p>
                          <p className="text-xs text-muted-foreground">
                            Deleted by {deletion.deletedBy} on {formatDate(deletion.deletionDate)}
                          </p>
                          <p className="text-xs text-gray-600 mt-1">
                            Reason: {deletion.reason}
                          </p>
                          {deletion.originalData?.reference && (
                            <p className="text-xs text-muted-foreground">
                              Reference: {deletion.originalData.reference}
                            </p>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge
                          variant={deletion.status === "Compliant" ? "default" : "secondary"}
                          className={deletion.status === "Compliant" ? "bg-green-100 text-green-700" : ""}
                        >
                          {deletion.status === "Compliant" ? (
                            <CheckCircle className="h-3 w-3 mr-1" />
                          ) : (
                            <Clock className="h-3 w-3 mr-1" />
                          )}
                          {deletion.status}
                        </Badge>
                      </div>
                    </div>
                    {index < recentDeletions.length - 1 && <Separator className="mt-4" />}
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <Database className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No recent deletions found</p>
                <p className="text-sm">All audit items are up to date</p>
              </div>
            )}
            <div className="mt-4 pt-4 border-t">
              <Link href="/dashboard/auditors/deleted-items">
                <Button variant="outline" size="sm" className="w-full">
                  View All Deleted Items
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>

        {/* Compliance Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5 text-green-600" />
              Government Compliance Information
            </CardTitle>
            <CardDescription>
              Teachers Council of Malawi audit compliance standards
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <h4 className="font-medium text-sm mb-2">Retention Policy</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• 7-year retention period (government standard)</li>
                  <li>• Secure encrypted storage</li>
                  <li>• Access limited to AUDITOR and SUPER_ADMIN roles</li>
                  <li>• Complete audit trail maintained</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium text-sm mb-2">Recovery Window</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• 90-day recovery window for deleted items</li>
                  <li>• Automatic expiry after deadline</li>
                  <li>• Recovery requires proper authorization</li>
                  <li>• Full recovery audit trail</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardShell>
  )
}
