// app/(dashboard)/dashboard/auditors/recovery-center/page.tsx
"use client"

import { useEffect, useState } from "react"
import { useSearchPara<PERSON>, useRouter } from "next/navigation"
import { DashboardShell } from "@/components/dashboard-shell"
import { DashboardHeader } from "@/components/dashboard-header"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Checkbox } from "@/components/ui/checkbox"
import {
  ArrowLeft,
  RotateCcw,
  AlertTriangle,
  CheckCircle,
  Clock,
  Shield,
  FileText,
  User,
  Database,
  RefreshCw
} from "lucide-react"
import Link from "next/link"
import { AuditDateFormatter } from "@/lib/utils/date-formatter"
import { useToast } from "@/hooks/use-toast"

interface RecoveryItem {
  id: string;
  originalId: string;
  originalModel: string;
  originalData: any;
  deletionDate: string;
  deletionReason: string;
  recoveryDeadline: string;
  deletedBy: {
    name: string;
    email: string;
    role: string;
  };
  canBeRecovered: boolean;
  reviewStatus: string;
}

export default function RecoveryCenterPage() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const { toast } = useToast()

  const [selectedItem, setSelectedItem] = useState<RecoveryItem | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [isRecovering, setIsRecovering] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Recovery form state
  const [recoveryReason, setRecoveryReason] = useState('')
  const [recoveryJustification, setRecoveryJustification] = useState('')
  const [acknowledgeCompliance, setAcknowledgeCompliance] = useState(false)
  const [acknowledgeResponsibility, setAcknowledgeResponsibility] = useState(false)
  const [acknowledgeAuditTrail, setAcknowledgeAuditTrail] = useState(false)

  const itemId = searchParams.get('itemId')

  // Fetch item details if itemId is provided
  useEffect(() => {
    if (itemId) {
      fetchItemForRecovery(itemId)
    }
  }, [itemId])

  const fetchItemForRecovery = async (id: string) => {
    try {
      setIsLoading(true)
      setError(null)

      const response = await fetch(`/api/audit/deleted-items/${id}`)
      if (!response.ok) {
        throw new Error('Failed to fetch item details')
      }

      const data = await response.json()
      setSelectedItem(data.item)
    } catch (error) {
      console.error('Error fetching item:', error)
      setError(error instanceof Error ? error.message : 'Failed to fetch item')
    } finally {
      setIsLoading(false)
    }
  }

  const handleRecovery = async () => {
    if (!selectedItem) return

    // Validation
    if (!recoveryReason.trim()) {
      toast({
        title: "Validation Error",
        description: "Recovery reason is required",
        variant: "destructive",
      })
      return
    }

    if (recoveryReason.length < 20) {
      toast({
        title: "Validation Error",
        description: "Recovery reason must be at least 20 characters",
        variant: "destructive",
      })
      return
    }

    if (!recoveryJustification.trim()) {
      toast({
        title: "Validation Error",
        description: "Recovery justification is required",
        variant: "destructive",
      })
      return
    }

    if (!acknowledgeCompliance || !acknowledgeResponsibility || !acknowledgeAuditTrail) {
      toast({
        title: "Validation Error",
        description: "All compliance acknowledgments are required",
        variant: "destructive",
      })
      return
    }

    try {
      setIsRecovering(true)

      const response = await fetch(`/api/audit/recovery`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          itemId: selectedItem.id,
          recoveryReason,
          recoveryJustification,
          acknowledgments: {
            compliance: acknowledgeCompliance,
            responsibility: acknowledgeResponsibility,
            auditTrail: acknowledgeAuditTrail
          }
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Recovery failed')
      }

      const result = await response.json()

      toast({
        title: "Recovery Successful",
        description: result.message,
      })

      // Redirect to success page or back to deleted items
      router.push(`/dashboard/auditors/deleted-items?recovered=${selectedItem.id}`)

    } catch (error) {
      console.error('Recovery error:', error)
      toast({
        title: "Recovery Failed",
        description: error instanceof Error ? error.message : "Failed to recover item",
        variant: "destructive",
      })
    } finally {
      setIsRecovering(false)
    }
  }

  const canRecover = selectedItem?.canBeRecovered &&
    AuditDateFormatter.daysUntil(selectedItem.recoveryDeadline) > 0 &&
    selectedItem.reviewStatus !== 'flagged'

  const daysUntilExpiry = selectedItem ? AuditDateFormatter.daysUntil(selectedItem.recoveryDeadline) : 0

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Recovery Center"
        text="Recover deleted items within the government-mandated recovery period"
      >
        <Link href="/dashboard/auditors/deleted-items">
          <Button variant="outline" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Deleted Items
          </Button>
        </Link>
      </DashboardHeader>

      <div className="space-y-6">
        {/* Recovery Guidelines */}
        <Alert>
          <Shield className="h-4 w-4" />
          <AlertDescription>
            <strong>Government Recovery Policy:</strong> Items can only be recovered within 90 days of deletion
            and must meet strict compliance requirements. All recovery actions are logged for audit purposes.
          </AlertDescription>
        </Alert>

        {isLoading && (
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-center py-8">
                <RefreshCw className="h-6 w-6 animate-spin mr-2" />
                Loading item details...
              </div>
            </CardContent>
          </Card>
        )}

        {error && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {!itemId && !selectedItem && (
          <Card>
            <CardHeader>
              <CardTitle>No Item Selected</CardTitle>
              <CardDescription>
                Please select an item from the deleted items list to begin recovery
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Link href="/dashboard/auditors/deleted-items">
                <Button>
                  <Database className="h-4 w-4 mr-2" />
                  Browse Deleted Items
                </Button>
              </Link>
            </CardContent>
          </Card>
        )}

        {selectedItem && (
          <>
            {/* Recovery Eligibility Status */}
            <Alert className={canRecover ? "border-green-200 bg-green-50" : "border-red-200 bg-red-50"}>
              {canRecover ? (
                <>
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <AlertDescription>
                    <strong>Recovery Available:</strong> This item can be recovered for {daysUntilExpiry} more days.
                    {daysUntilExpiry <= 7 && " Recovery deadline approaching!"}
                  </AlertDescription>
                </>
              ) : (
                <>
                  <AlertTriangle className="h-4 w-4 text-red-600" />
                  <AlertDescription>
                    <strong>Recovery Not Available:</strong>
                    {daysUntilExpiry <= 0 ? " Recovery period has expired." :
                     selectedItem.reviewStatus === 'flagged' ? " Item is flagged and cannot be recovered." :
                     " Item is not eligible for recovery."}
                  </AlertDescription>
                </>
              )}
            </Alert>

            {/* Item Details */}
            <div className="grid gap-6 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Database className="h-5 w-5" />
                    Item Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">Item Type</Label>
                    <p className="text-sm font-medium">{selectedItem.originalModel}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">Original ID</Label>
                    <p className="text-sm font-mono">{selectedItem.originalId}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">Reference</Label>
                    <p className="text-sm">{selectedItem.originalData?.reference || selectedItem.originalData?.name || 'N/A'}</p>
                  </div>
                  {selectedItem.originalData?.amount && (
                    <div>
                      <Label className="text-sm font-medium text-muted-foreground">Amount</Label>
                      <p className="text-sm font-medium">
                        {new Intl.NumberFormat('en-MW', {
                          style: 'currency',
                          currency: 'MWK'
                        }).format(selectedItem.originalData.amount)}
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <User className="h-5 w-5" />
                    Deletion Details
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">Deleted By</Label>
                    <p className="text-sm">{selectedItem.deletedBy.name}</p>
                    <p className="text-xs text-muted-foreground">{selectedItem.deletedBy.role}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">Deletion Date</Label>
                    <p className="text-sm">{AuditDateFormatter.dashboard(selectedItem.deletionDate)}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">Recovery Deadline</Label>
                    <p className="text-sm">{AuditDateFormatter.dashboard(selectedItem.recoveryDeadline)}</p>
                    <p className="text-xs text-muted-foreground">
                      {daysUntilExpiry > 0 ? `${daysUntilExpiry} days remaining` : 'Expired'}
                    </p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">Original Deletion Reason</Label>
                    <div className="bg-muted/50 rounded-md p-2 mt-1">
                      <p className="text-xs">{selectedItem.deletionReason}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Recovery Form */}
            {canRecover && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <RotateCcw className="h-5 w-5" />
                    Recovery Request
                  </CardTitle>
                  <CardDescription>
                    Complete the form below to request recovery of this item
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-2">
                    <Label htmlFor="recoveryReason">Recovery Reason *</Label>
                    <Input
                      id="recoveryReason"
                      placeholder="Brief reason for recovery (minimum 20 characters)"
                      value={recoveryReason}
                      onChange={(e) => setRecoveryReason(e.target.value)}
                      className={recoveryReason.length > 0 && recoveryReason.length < 20 ? "border-red-300" : ""}
                    />
                    <p className="text-xs text-muted-foreground">
                      {recoveryReason.length}/20 characters minimum
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="recoveryJustification">Detailed Justification *</Label>
                    <Textarea
                      id="recoveryJustification"
                      placeholder="Provide detailed justification for why this item needs to be recovered..."
                      value={recoveryJustification}
                      onChange={(e) => setRecoveryJustification(e.target.value)}
                      rows={4}
                    />
                    <p className="text-xs text-muted-foreground">
                      Explain the business need and impact of recovery
                    </p>
                  </div>

                  <Separator />

                  <div className="space-y-4">
                    <h4 className="font-medium">Compliance Acknowledgments</h4>

                    <div className="flex items-start space-x-2">
                      <Checkbox
                        id="compliance"
                        checked={acknowledgeCompliance}
                        onCheckedChange={(checked) => setAcknowledgeCompliance(checked as boolean)}
                      />
                      <Label htmlFor="compliance" className="text-sm leading-5">
                        I acknowledge that this recovery complies with government audit standards and
                        Teachers Council of Malawi policies.
                      </Label>
                    </div>

                    <div className="flex items-start space-x-2">
                      <Checkbox
                        id="responsibility"
                        checked={acknowledgeResponsibility}
                        onCheckedChange={(checked) => setAcknowledgeResponsibility(checked as boolean)}
                      />
                      <Label htmlFor="responsibility" className="text-sm leading-5">
                        I take full responsibility for this recovery action and understand that
                        it will be logged in the audit trail.
                      </Label>
                    </div>

                    <div className="flex items-start space-x-2">
                      <Checkbox
                        id="auditTrail"
                        checked={acknowledgeAuditTrail}
                        onCheckedChange={(checked) => setAcknowledgeAuditTrail(checked as boolean)}
                      />
                      <Label htmlFor="auditTrail" className="text-sm leading-5">
                        I understand that this recovery will create a permanent audit trail entry
                        that cannot be modified or deleted.
                      </Label>
                    </div>
                  </div>

                  <Separator />

                  <div className="flex gap-3">
                    <Button
                      onClick={handleRecovery}
                      disabled={isRecovering}
                      className="bg-green-600 hover:bg-green-700"
                    >
                      {isRecovering ? (
                        <>
                          <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                          Processing Recovery...
                        </>
                      ) : (
                        <>
                          <RotateCcw className="h-4 w-4 mr-2" />
                          Recover Item
                        </>
                      )}
                    </Button>
                    <Link href="/dashboard/auditors/deleted-items">
                      <Button variant="outline">Cancel</Button>
                    </Link>
                  </div>
                </CardContent>
              </Card>
            )}
          </>
        )}
      </div>
    </DashboardShell>
  )
}
