'use client';

import React, { useState, useEffect } from 'react';
import { DashboardShell } from "@/components/dashboard-shell";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Textarea } from "@/components/ui/textarea";
import {
  ArrowLeft,
  CheckCircle,
  XCircle,
  Clock,
  MoreHorizontal,
  RefreshCw,
  Eye,
  FileText,
  Trash2
} from "lucide-react";
import { AuditDeletionDialog } from "@/components/ui/audit-deletion-dialog";
import { AuditDeletionUI } from "@/lib/utils/audit-deletion-ui";
import Link from "next/link";
import { useToast } from "@/hooks/use-toast";
import { format } from "date-fns";
import { useIncome } from "@/lib/hooks/accounting/use-income";

interface DraftIncome {
  _id: string;
  reference: string;
  source: string;
  amount: number;
  description?: string;
  fiscalYear: string;
  status: string;
  createdAt: string;
  createdBy: {
    name: string;
    email: string;
  };
  budget?: {
    name: string;
    fiscalYear: string;
  };
  budgetCategory?: {
    name: string;
    type: string;
  };
}

interface DraftData {
  draftIncome: DraftIncome[];
  summary: {
    draft: { count: number; totalAmount: number };
    pending_approval: { count: number; totalAmount: number };
    rejected: { count: number; totalAmount: number };
  };
  totals: {
    count: number;
    amount: number;
  };
  pagination: {
    totalCount: number;
    totalPages: number;
    currentPage: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
  filters: {
    fiscalYears: string[];
    statuses: Array<{
      value: string;
      label: string;
      count: number;
    }>;
  };
}

export function IncomeDraftsPage() {
  const [data, setData] = useState<DraftData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [statusFilter, setStatusFilter] = useState('draft');
  const [fiscalYearFilter, setFiscalYearFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  
  // Action dialog state
  const [showActionDialog, setShowActionDialog] = useState(false);
  const [actionType, setActionType] = useState<'approve' | 'reject' | 'receive' | 'cancel' | 'delete'>('approve');
  const [actionNotes, setActionNotes] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);

  // Audit deletion dialog state
  const [showAuditDeletionDialog, setShowAuditDeletionDialog] = useState(false);

  const { toast } = useToast();

  // Fetch draft data
  const fetchDraftData = async () => {
    try {
      setIsLoading(true);
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '20',
        status: statusFilter,
        ...(fiscalYearFilter !== 'all' && { fiscalYear: fiscalYearFilter })
      });

      const response = await fetch(`/api/accounting/income/drafts?${params}`);
      if (!response.ok) {
        throw new Error('Failed to fetch draft income data');
      }

      const result = await response.json();
      setData(result);
    } catch (error) {
      console.error('Error fetching draft data:', error);
      toast({
        title: "Error",
        description: "Failed to fetch draft income data",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Load data on component mount and filter changes
  useEffect(() => {
    fetchDraftData();
  }, [currentPage, statusFilter, fiscalYearFilter]);

  // Get the status update mutation from useIncome hook
  const { updateIncomeStatus } = useIncome();

  // Handle individual status update
  const handleStatusUpdate = (incomeId: string, newStatus: string, notes?: string) => {
    updateIncomeStatus.mutate({
      id: incomeId,
      status: newStatus,
      reason: notes || `Status updated to ${newStatus}`,
    }, {
      onSuccess: () => {
        // Refresh draft data after successful status update
        fetchDraftData();
      }
    });
  };

  // Handle bulk action (non-delete actions)
  const handleBulkAction = async () => {
    if (selectedItems.length === 0) {
      toast({
        title: "Error",
        description: "Please select items to perform bulk action",
        variant: "destructive",
      });
      return;
    }

    // Validate action type for non-delete actions
    const validActions = ['approve', 'reject', 'receive', 'cancel'];
    if (!validActions.includes(actionType)) {
      toast({
        title: "Error",
        description: `Invalid action: ${actionType}. Valid actions are: ${validActions.join(', ')}`,
        variant: "destructive",
      });
      return;
    }

    try {
      setIsProcessing(true);

      // Use existing drafts API for non-delete actions
      const response = await fetch('/api/accounting/income/drafts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          incomeIds: selectedItems,
          action: actionType,
          notes: actionNotes,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to perform bulk action');
      }

      const result = await response.json();
      toast({
        title: "Success",
        description: result.message,
      });

      // Reset selections and refresh data
      setSelectedItems([]);
      setShowActionDialog(false);
      setActionNotes('');
      fetchDraftData();
    } catch (error) {
      console.error('Error performing bulk action:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to perform bulk action",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle deletion using audit deletion utilities
  const handleAuditDeletion = async (deletionReason: string) => {
    try {
      setIsProcessing(true);

      // Use the audit deletion UI utility for proper handling
      const result = await AuditDeletionUI.handleAuditDeletion(
        '/api/audit/delete/bulk',
        selectedItems,
        deletionReason,
        'income draft',
        {
          minLength: 10,
          maxLength: 500,
          title: `Delete ${selectedItems.length} Income Draft${selectedItems.length > 1 ? 's' : ''}`,
          description: 'This action will move the selected income drafts to the audit trail for compliance purposes.',
        },
        {
          modelName: 'Income',
          additionalContext: {
            module: 'income-drafts',
            action: 'bulk-delete'
          }
        }
      );

      if (result.success) {
        // Reset selections and refresh data
        setSelectedItems([]);
        setShowAuditDeletionDialog(false);
        fetchDraftData();
      }
    } catch (error) {
      console.error('Error performing audit deletion:', error);
      AuditDeletionUI.showErrorToast(
        error instanceof Error ? error.message : "Failed to delete items"
      );
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle action routing - routes to correct handler based on action type
  const handleActionExecution = async () => {
    if (actionType === 'delete') {
      // For delete actions, show the audit deletion dialog
      setShowActionDialog(false);
      setShowAuditDeletionDialog(true);
    } else {
      // For other actions, use the bulk action handler
      await handleBulkAction();
    }
  };

  // Handle select all
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedItems(data?.draftIncome.map(item => item._id) || []);
    } else {
      setSelectedItems([]);
    }
  };

  // Handle individual selection
  const handleSelectItem = (itemId: string, checked: boolean) => {
    if (checked) {
      setSelectedItems(prev => [...prev, itemId]);
    } else {
      setSelectedItems(prev => prev.filter(id => id !== itemId));
    }
  };

  // Get status badge variant
  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'draft': return 'secondary';
      case 'pending_approval': return 'default';
      case 'rejected': return 'destructive';
      default: return 'outline';
    }
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-MW', {
      style: 'currency',
      currency: 'MWK',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  // Filter data based on search term
  const filteredData = data?.draftIncome.filter(item =>
    item.reference.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.source.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.description?.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  return (
    <DashboardShell>
      <div className="flex flex-col gap-6">
        {/* Header */}
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Income Drafts & Approvals</h1>
            <p className="text-muted-foreground">
              Review and approve draft income entries for the Teachers Council of Malawi
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" asChild>
              <Link href="/dashboard/accounting/income/overview">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Income
              </Link>
            </Button>
            <Button variant="outline" size="sm" onClick={fetchDraftData}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>
        </div>

        {/* Summary Cards */}
        {data && (
          <div className="grid gap-4 md:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Draft</CardTitle>
                <FileText className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{data.summary.draft.count}</div>
                <p className="text-xs text-muted-foreground">
                  {formatCurrency(data.summary.draft.totalAmount)}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Pending Approval</CardTitle>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{data.summary.pending_approval.count}</div>
                <p className="text-xs text-muted-foreground">
                  {formatCurrency(data.summary.pending_approval.totalAmount)}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Rejected</CardTitle>
                <XCircle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{data.summary.rejected.count}</div>
                <p className="text-xs text-muted-foreground">
                  {formatCurrency(data.summary.rejected.totalAmount)}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Pending</CardTitle>
                <Eye className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{data.totals.count}</div>
                <p className="text-xs text-muted-foreground">
                  {formatCurrency(data.totals.amount)}
                </p>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Filters and Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Filters and Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
              <div className="flex flex-col gap-2 sm:flex-row sm:items-center">
                <Input
                  placeholder="Search by reference, source, or description..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full sm:w-64"
                />
                
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-full sm:w-48">
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="draft">Draft</SelectItem>
                    <SelectItem value="pending_approval">Pending Approval</SelectItem>
                    <SelectItem value="rejected">Rejected</SelectItem>
                  </SelectContent>
                </Select>

                {data && (
                  <Select value={fiscalYearFilter} onValueChange={setFiscalYearFilter}>
                    <SelectTrigger className="w-full sm:w-48">
                      <SelectValue placeholder="Filter by fiscal year" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Fiscal Years</SelectItem>
                      {data.filters.fiscalYears.map(year => (
                        <SelectItem key={year} value={year}>{year}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              </div>

              {selectedItems.length > 0 && (
                <div className="flex items-center gap-2">
                  <span className="text-sm text-muted-foreground">
                    {selectedItems.length} selected
                  </span>
                  <Button
                    size="sm"
                    onClick={() => {
                      setActionType('approve');
                      setShowActionDialog(true);
                    }}
                  >
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Approve Selected
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => {
                      setActionType('reject');
                      setShowActionDialog(true);
                    }}
                  >
                    <XCircle className="h-4 w-4 mr-2" />
                    Reject Selected
                  </Button>
                  <Button
                    size="sm"
                    variant="destructive"
                    onClick={() => {
                      setActionType('delete');
                      setShowActionDialog(true);
                    }}
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete Selected
                  </Button>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Data Table */}
        <Card>
          <CardHeader>
            <CardTitle>Income Entries</CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <RefreshCw className="h-8 w-8 animate-spin" />
              </div>
            ) : filteredData.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-muted-foreground">No income entries found</p>
              </div>
            ) : (
              <div className="space-y-4">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-12">
                        <Checkbox
                          checked={selectedItems.length === filteredData.length}
                          onCheckedChange={handleSelectAll}
                        />
                      </TableHead>
                      <TableHead>Reference</TableHead>
                      <TableHead>Source</TableHead>
                      <TableHead>Amount</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Created By</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredData.map((item) => (
                      <TableRow key={item._id}>
                        <TableCell>
                          <Checkbox
                            checked={selectedItems.includes(item._id)}
                            onCheckedChange={(checked) => handleSelectItem(item._id, checked as boolean)}
                          />
                        </TableCell>
                        <TableCell className="font-medium">{item.reference}</TableCell>
                        <TableCell>{item.source.replace('_', ' ')}</TableCell>
                        <TableCell>{formatCurrency(item.amount)}</TableCell>
                        <TableCell>
                          <Badge variant={getStatusBadgeVariant(item.status)}>
                            {item.status.replace('_', ' ')}
                          </Badge>
                        </TableCell>
                        <TableCell>{item.createdBy.name}</TableCell>
                        <TableCell>{format(new Date(item.createdAt), 'PPP')}</TableCell>
                        <TableCell>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem
                                onClick={() => handleStatusUpdate(item._id, 'received')}
                              >
                                <CheckCircle className="h-4 w-4 mr-2" />
                                Mark as Received
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() => handleStatusUpdate(item._id, 'approved')}
                              >
                                <CheckCircle className="h-4 w-4 mr-2" />
                                Approve
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem
                                onClick={() => handleStatusUpdate(item._id, 'rejected', 'Individual rejection')}
                                className="text-destructive"
                              >
                                <XCircle className="h-4 w-4 mr-2" />
                                Reject
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>

                {/* Pagination */}
                {data && data.pagination.totalPages > 1 && (
                  <div className="flex items-center justify-between">
                    <p className="text-sm text-muted-foreground">
                      Page {data.pagination.currentPage} of {data.pagination.totalPages}
                    </p>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                        disabled={!data.pagination.hasPreviousPage}
                      >
                        Previous
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentPage(prev => prev + 1)}
                        disabled={!data.pagination.hasNextPage}
                      >
                        Next
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Bulk Action Dialog */}
        <AlertDialog open={showActionDialog} onOpenChange={setShowActionDialog}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>
                {actionType === 'approve' ? 'Approve' :
                 actionType === 'reject' ? 'Reject' :
                 actionType === 'receive' ? 'Mark as Received' :
                 actionType === 'delete' ? 'Delete' : 'Cancel'} Selected Items
              </AlertDialogTitle>
              <AlertDialogDescription>
                You are about to {actionType} {selectedItems.length} income entries.
                {actionType === 'approve' || actionType === 'receive'
                  ? ' This will make them visible in the main income overview and budget planning pages.'
                  : actionType === 'delete'
                  ? ' This will open the audit deletion dialog where you must provide a detailed reason for compliance purposes.'
                  : ' This action can be undone by changing the status later.'
                }
              </AlertDialogDescription>
            </AlertDialogHeader>
            <div className="py-4">
              <Textarea
                placeholder={`Add notes for this ${actionType} action (optional)`}
                value={actionNotes}
                onChange={(e) => setActionNotes(e.target.value)}
              />
            </div>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleActionExecution}
                disabled={isProcessing}
                className={actionType === 'reject' || actionType === 'delete' ? 'bg-destructive text-destructive-foreground hover:bg-destructive/90' : ''}
              >
                {isProcessing ? 'Processing...' : `${actionType === 'approve' ? 'Approve' :
                  actionType === 'reject' ? 'Reject' :
                  actionType === 'receive' ? 'Mark as Received' :
                  actionType === 'delete' ? 'Continue to Deletion' : 'Cancel'} ${actionType === 'delete' ? '' : selectedItems.length + ' Items'}`}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

        {/* Audit Deletion Dialog */}
        <AuditDeletionDialog
          open={showAuditDeletionDialog}
          onOpenChange={setShowAuditDeletionDialog}
          onConfirm={handleAuditDeletion}
          selectedCount={selectedItems.length}
          itemType="income draft"
          isProcessing={isProcessing}
          showComplianceInfo={true}
        />
      </div>
    </DashboardShell>
  );
}
