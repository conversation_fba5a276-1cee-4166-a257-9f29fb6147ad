// app/api/audit/deleted-items/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { connectToDatabase } from '@/lib/database';
import DeletedItem from '@/models/audit/DeletedItems';
import User from '@/models/User';

export const runtime = 'nodejs';

/**
 * GET /api/audit/deleted-items
 * Get deleted items with filtering and pagination
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions - only auditors and super admins can access
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.AUDITOR
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions to access audit data' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Ensure models are registered
    User;

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const skip = (page - 1) * limit;

    // Build filter
    const filter: Record<string, any> = {};

    // Filter by model type
    const modelType = searchParams.get('modelType');
    if (modelType && modelType !== 'all') {
      filter.originalModel = modelType;
    }

    // Filter by date range
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    if (startDate || endDate) {
      filter.deletedAt = {};
      if (startDate) filter.deletedAt.$gte = new Date(startDate);
      if (endDate) filter.deletedAt.$lte = new Date(endDate);
    }

    // Filter by deleted by user
    const deletedBy = searchParams.get('deletedBy');
    if (deletedBy) {
      filter.deletedBy = deletedBy;
    }

    // Filter by fiscal year
    const fiscalYear = searchParams.get('fiscalYear');
    if (fiscalYear && fiscalYear !== 'all') {
      filter.fiscalYear = fiscalYear;
    }

    // Filter by review status
    const reviewStatus = searchParams.get('reviewStatus');
    if (reviewStatus && reviewStatus !== 'all') {
      filter.reviewStatus = reviewStatus;
    }

    // Search in deletion reason
    const search = searchParams.get('search');
    if (search) {
      filter.$or = [
        { deletionReason: { $regex: search, $options: 'i' } },
        { 'originalData.reference': { $regex: search, $options: 'i' } },
        { 'deletedByUser.name': { $regex: search, $options: 'i' } }
      ];
    }

    // Get total count for pagination
    const totalCount = await DeletedItem.countDocuments(filter);
    const totalPages = Math.ceil(totalCount / limit);

    // Fetch deleted items
    const deletedItems = await DeletedItem.find(filter)
      .sort({ deletedAt: -1 }) // Most recent first
      .skip(skip)
      .limit(limit)
      .populate('deletedBy', 'firstName lastName email role')
      .populate('reviewedBy', 'firstName lastName email role')
      .lean();

    // Get summary statistics
    const summaryStats = await DeletedItem.aggregate([
      {
        $group: {
          _id: '$originalModel',
          count: { $sum: 1 },
          totalItems: { $sum: 1 }
        }
      }
    ]);

    // Get review status summary
    const reviewStatusStats = await DeletedItem.aggregate([
      {
        $group: {
          _id: '$reviewStatus',
          count: { $sum: 1 }
        }
      }
    ]);

    // Get recent deletions (last 7 days)
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    const recentDeletionsCount = await DeletedItem.countDocuments({
      deletedAt: { $gte: sevenDaysAgo }
    });

    // Get items nearing recovery deadline
    const thirtyDaysFromNow = new Date();
    thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);
    const nearingDeadlineCount = await DeletedItem.countDocuments({
      recoveryDeadline: { $lte: thirtyDaysFromNow },
      canBeRecovered: true
    });

    // Format summary data
    const summary = {
      byModel: summaryStats.reduce((acc, stat) => {
        acc[stat._id] = { count: stat.count };
        return acc;
      }, {} as Record<string, { count: number }>),
      byReviewStatus: reviewStatusStats.reduce((acc, stat) => {
        acc[stat._id] = { count: stat.count };
        return acc;
      }, {} as Record<string, { count: number }>),
      recentDeletions: recentDeletionsCount,
      nearingDeadline: nearingDeadlineCount
    };

    // Get available fiscal years for filtering
    const fiscalYears = await DeletedItem.distinct('fiscalYear');

    // Get available model types for filtering
    const modelTypes = await DeletedItem.distinct('originalModel');

    return NextResponse.json({
      deletedItems,
      summary,
      pagination: {
        totalCount,
        totalPages,
        currentPage: page,
        limit,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1
      },
      filters: {
        fiscalYears: fiscalYears.filter(Boolean).sort().reverse(),
        modelTypes: modelTypes.sort(),
        reviewStatuses: [
          { value: 'pending', label: 'Pending Review' },
          { value: 'approved', label: 'Approved' },
          { value: 'flagged', label: 'Flagged' },
          { value: 'investigated', label: 'Investigated' }
        ]
      }
    });

  } catch (error) {
    console.error('Error fetching deleted items:', error);
    return NextResponse.json(
      { error: 'An error occurred while fetching deleted items' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/audit/deleted-items
 * Update review status of deleted items
 */
export async function PUT(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.AUDITOR
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await req.json();
    const { ids, reviewStatus, auditNotes } = body;

    // Validate required fields
    if (!ids || !Array.isArray(ids) || ids.length === 0 || !reviewStatus) {
      return NextResponse.json(
        { error: 'ids (array) and reviewStatus are required' },
        { status: 400 }
      );
    }

    // Update deleted items
    const updateResult = await DeletedItem.updateMany(
      { _id: { $in: ids } },
      {
        $set: {
          reviewStatus,
          reviewedBy: user.id,
          reviewedAt: new Date(),
          ...(auditNotes && { auditNotes })
        }
      }
    );

    return NextResponse.json({
      success: true,
      message: `Updated ${updateResult.modifiedCount} deleted items`,
      updatedCount: updateResult.modifiedCount
    });

  } catch (error) {
    console.error('Error updating deleted items:', error);
    return NextResponse.json(
      { error: 'An error occurred while updating deleted items' },
      { status: 500 }
    );
  }
}
